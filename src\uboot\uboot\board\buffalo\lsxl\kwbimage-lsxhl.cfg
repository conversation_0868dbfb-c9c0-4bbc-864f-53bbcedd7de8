# SPDX-License-Identifier: GPL-2.0+
#
# Copyright (c) 2012 <PERSON>
# <PERSON> <<EMAIL>>
# Refer doc/README.kwbimage for more details about how-to configure
# and create kirkwood boot image
#

# Boot Media configurations
BOOT_FROM spi

# SOC registers configuration using bootrom header extension
# Maximum KWBIMAGE_MAX_CONFIG configurations allowed

# Configure RGMII-0/1 interface pad voltage to 1.8V
DATA 0xFFD100E0 0x1B1B9B9B

# L2 RAM Timing 0
DATA 0xFFD20134 0xBBBBBBBB
# not further specified in HW manual, timing taken from original vendor port

# L2 RAM Timing 1
DATA 0xFFD20138 0x00BBBBBB
# not further specified in HW manual, timing taken from original vendor port

# DDR Configuration register
DATA 0xFFD01400 0x43000618
# bit13-0:  0x618, 1560 DDR2 clks refresh rate
# bit23-14: 0 required
# bit24:    1, enable exit self refresh mode on DDR access
# bit25:    1 required
# bit29-26: 0 required
# bit31-30: 0b01 required

# DDR Controller Control Low
DATA 0xFFD01404 0x39543010
# bit3-0:   0 required
# bit4:     1, T2 mode, addr/cmd are driven for two cycles
# bit5:     0, clk is driven during self refresh, we don't care for APX
# bit6:     0, use recommended falling edge of clk for addr/cmd
# bit11-7:  0 required
# bit12:    1 required
# bit13:    1 required
# bit14:    0, input buffer always powered up
# bit17-15: 0 required
# bit18:    1, cpu lock transaction enabled
# bit19:    0 required
# bit23-20: 5, recommended value for CL=5 and STARTBURST_DEL disabled bit31=0
# bit27-24: 9, CL+4, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
# bit30-28: 3 required
# bit31:    0, no additional STARTBURST delay

# DDR Timing (Low)
DATA 0xFFD01408 0x22125441
# bit3-0:   0x1, 18 cycle tRAS (tRAS[3-0])
# bit7-4:   4, 5 cycle tRCD
# bit11-8:  4, 5 cyle tRP
# bit15-12: 5, 6 cyle tWR
# bit19-16: 2, 3 cyle tWTR
# bit20:    1, 18 cycle tRAS (tRAS[4])
# bit23-21: 0 required
# bit27-24: 2, 3 cycle tRRD
# bit31-28: 2, 3 cyle tRTP

# DDR Timing (High)
DATA 0xFFD0140C 0x00000832
# bit6-0:   0x32, 50 cycle tRFC
# bit8-7:   0, 1 cycle tR2R
# bit10-9:  0, 1 cyle tR2W
# bit12-11: 1, 2 cylce tW2W
# bit31-13: 0 required

# DDR Address Control
DATA 0xFFD01410 0x0000000C
# bit1-0:   0, Cs0width=x8
# bit3-2:   3, Cs0size=1Gbit
# bit5-4:   0, Cs1width=nonexistent
# bit7-6:   0, Cs1size=nonexistent
# bit9-8:   0, Cs2width=nonexistent
# bit11-10: 0, Cs2size=nonexistent
# bit13-12: 0, Cs3width=nonexistent
# bit15-14: 0, Cs3size=nonexistent
# bit16:    0, Cs0AddrSel
# bit17:    0, Cs1AddrSel
# bit18:    0, Cs2AddrSel
# bit19:    0, Cs3AddrSel
# bit31-20: 0 required

# DDR Open Pages Control
DATA 0xFFD01414 0x00000000
# bit0:    0, OPEn=OpenPage enabled
# bit31-1: 0 required

# DDR Operation
DATA 0xFFD01418 0x00000000
# bit3-0:   0, Cmd=Normal SDRAM Mode
# bit31-4:  0 required

# DDR Mode
DATA 0xFFD0141C 0x00000652
# bit2-0:   2, Burst Length (2 required)
# bit3:     0, Burst Type (0 required)
# bit6-4:   5, CAS Latency (CL) 5
# bit7:     0, (Test Mode) Normal operation
# bit8:     0, (Reset DLL) Normal operation
# bit11-9:  3, Write recovery for auto-precharge (3 required)
# bit12:    0, Fast Active power down exit time (0 required)
# bit31-13: 0 required

# DDR Extended Mode
DATA 0xFFD01420 0x00000006
# bit0:     0, DRAM DLL enabled
# bit1:     1, DRAM drive strength reduced
# bit2:     1, ODT control Rtt[0] (Rtt=1, 75 ohm termination)
# bit5-3:   0 required
# bit6:     0, ODT control Rtt[1] (Rtt=1, 75 ohm termination)
# bit9-7:   0 required
# bit10:    0, differential DQS enabled
# bit11:    0 required
# bit12:    0, DRAM output buffer enabled
# bit31-13: 0 required

# DDR Controller Control High
DATA 0xFFD01424 0x0000F17F
# bit2-0:   0x7 required
# bit3:     1, MBUS Burst Chop disabled
# bit6-4:   0x7 required
# bit7:     0 required (???)
# bit8:     1, add writepath sample stage, must be 1 for DDR freq >= 300MHz
# bit9:     0, no half clock cycle addition to dataout
# bit10:    0, 1/4 clock cycle skew enabled for addr/ctl signals
# bit11:    0, 1/4 clock cycle skew disabled for write mesh
# bit15-12: 0xf required
# bit31-16: 0 required

# DDR2 ODT Read Timing (default values)
DATA 0xFFD01428 0x00085520
# bit3-0:   0 required
# bit7-4:   2, 2 cycles from read command to assertion of M_ODT signal
# bit11-8:  5, 5 cycles from read command to de-assertion of M_ODT signal
# bit15-12: 5, 5 cycles from read command to assertion of internal ODT signal
# bit19-16: 8, 8 cycles from read command to de-assertion of internal ODT signal
# bit31-20: 0 required

# DDR2 ODT Write Timing (default values)
DATA 0xFFD0147C 0x00008552
# bit3-0:   2, 2 cycles from write comand to assertion of M_ODT signal
# bit7-4:   5, 5 cycles from write command to de-assertion of M_ODT signal
# bit15-12: 5, 5 cycles from write command to assertion of internal ODT signal
# bit19-16: 8, 8 cycles from write command to de-assertion of internal ODT signal
# bit31-16: 0 required

# CS[0]n Base address
DATA 0xFFD01500 0x00000000
# at 0x0

# CS[0]n Size
DATA 0xFFD01504 0x0FFFFFF1
# bit0:     1, Window enabled
# bit1:     0, Write Protect disabled
# bit3-2:   0x0, CS0 hit selected
# bit23-4:  0xfffff required
# bit31-24: 0x0f, Size (i.e. 256MB)

# CS[1]n Size
DATA 0xFFD0150C 0x00000000
# window disabled

# CS[2]n Size
DATA 0xFFD01514 0x00000000
# window disabled

# CS[3]n Size
DATA 0xFFD0151C 0x00000000
# window disabled

# DDR ODT Control (Low)
DATA 0xFFD01494 0x00010000
# bit3-0:   0b0000, (read) M_ODT[0] is not asserted during read from DRAM
# bit7-4:   0b0000, (read) M_ODT[1] is not asserted during read from DRAM
# bit15-8:  0 required
# bit19-16: 0b0001, (write) M_ODT[0] is asserted during write to DRAM CS0
# bit23-20: 0b0000, (write) M_ODT[1] is not asserted during write to DRAM
# bit31-24: 0 required

# DDR ODT Control (High)
DATA 0xFFD01498 0x00000000
# bit1-0:   0, M_ODT[0] assertion is controlled by ODT Control Low register
# bit3-2:   0, M_ODT[1] assertion is controlled by ODT Control Low register
# bit31-4   0 required

# CPU ODT Control
DATA 0xFFD0149C 0x0000E80F
# bit3-0:   0b1111, internal ODT is asserted during read from DRAM bank 0-3
# bit7-4:   0b0000, internal ODT is not asserted during write to DRAM bank 0-3
# bit9-8:   0, Internal ODT assertion is controlled by fiels
# bit11-10: 2, M_DQ, M_DM, and M_DQS I/O buffer ODT 75 ohm
# bit13-12: 2, M_STARTBURST_IN I/O buffer ODT 75 ohm
# bit14:    1, M_STARTBURST_IN ODT enabled
# bit15:    1, DDR IO ODT Unit: Drive ODT calibration values
# bit20-16: 0, Pad N channel driving strength for ODT
# bit25-21: 0, Pad P channel driving strength for ODT
# bit31-26: 0 required

# DDR Initialization Control
DATA 0xFFD01480 0x00000001
# bit0:     1, enable DDR init upon this register write
# bit31-1:  0, required

# End of Header extension
DATA 0x0 0x0
