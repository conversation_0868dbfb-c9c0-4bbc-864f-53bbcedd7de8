CONFIG_BOARD_CONFIG_NAME="k230_rtos_01studio_defconfig"
CONFIG_BOARD_K230_CANMV_01STUDIO=y
CONFIG_BOARD_NAME="RtSmart-K230_01Studio"
CONFIG_BOARD_GEN_IMAGE_CFG_FILE="genimage-sdcard-rtos.cfg"
CONFIG_RTT_AUTO_EXEC_CMD="/sdcard/app/examples/integrated_poc/smart_ipc.elf -K /sdcard/app/examples/integrated_poc/face_detection_320.kmodel -C 1"
CONFIG_RTT_ENABLE_BUILD_EXAMPLES=y
CONFIG_RTT_ENABLE_BUILD_INTEGRATED_EXAMPLES=y
CONFIG_MPP_DEFAULT_SENSOR_CSI_2=y
CONFIG_MPP_ENABLE_CSI_DEV_0=y
CONFIG_MPP_CSI_DEV0_RESET=10
CONFIG_MPP_ENABLE_CSI_DEV_1=y
CONFIG_MPP_CSI_DEV1_RESET=45
CONFIG_MPP_ENABLE_CSI_DEV_2=y
CONFIG_MPP_CSI_DEV2_RESET=62
CONFIG_MPP_CSI_DEV2_I2C_DEV="i2c4"
CONFIG_MPP_CSI_DEV2_MCLK_3=y
CONFIG_MPP_ENABLE_SENSOR_GC2093=y
CONFIG_MPP_SENSOR_GC2093_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_SENSOR_OV5647=y
CONFIG_MPP_SENSOR_OV5647_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_SENSOR_IMX335=y
CONFIG_MPP_SENSOR_IMX335_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_SENSOR_SC132GS=y
CONFIG_MPP_ENABLE_SENSOR_SC132GS_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_SENSOR_BF3238=y
CONFIG_MPP_ENABLE_SENSOR_BF3238_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_DSI_HDMI=y
CONFIG_MPP_ENABLE_DSI_LCD=y
CONFIG_MPP_DSI_ENABLE_HDMI_LT9611=y
CONFIG_MPP_DSI_ENABLE_LCD_HX8399=y
CONFIG_MPP_DSI_ENABLE_LCD_ST7701=y
