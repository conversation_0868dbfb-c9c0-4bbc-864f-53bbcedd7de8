/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (C) 2013 Boundary Devices
 *
 * Refer doc/imx/mkimage/imximage.txt for more details about how-to configure
 * and create imximage boot image
 *
 * The syntax is taken as close as possible with the kwbimage
 */

/* image version */
IMAGE_VERSION 2

/*
 * Boot Device : one of
 * spi, sd (the board has no nand neither onenand)
 */
BOOT_FROM      spi

#include <config.h>
#ifdef CONFIG_IMX_HAB
CSF CONFIG_CSF_SIZE
#endif
#include "asm/arch/mx6-ddr.h"
#include "asm/arch/iomux.h"
#include "asm/arch/crm_regs.h"

#include "ddr-setup.cfg"
#include "1066mhz_4x128mx16.cfg"
#include "clocks.cfg"
