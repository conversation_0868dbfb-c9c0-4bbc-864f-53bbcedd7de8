File:		README.COBRA5272
Author:		<PERSON><PERSON><PERSON> for Sentec elektronik (<EMAIL>)
Contents:	This is the README of u-boot (Universal bootloader) for our
		COBRA5272 board.
Version:	v01.00
Date:		Tue Mar 30 00:28:33 CEST 2004
License:	This document is published under the GNU GPL
______________________________________________________________________

CHANGES
040330   v01.00 Creation

______________________________________________________________________


CONFIGURING
-----------

1. Modify include/configs/cobra5272.h acc. to your prefs

2. If necessary, modify board/cobra5272/config.mk (see below)

3.

> make cobra5272_config

> make


Please refer to u-boot README (general info, u-boot-x-x-x/README),
to u-boot-x-x-x/board/cobra5272/README and
to the comments in u-boot-x-x-x/include/configs/cobra5272.h

Configuring u-boot is done by commenting/uncommenting preprocessor defines.

Default configuration is

	FLASH version (for further info see subsection below)
	link address 0xffe00000

	16 MB RAM

	network enabled
	no default IP address for target, host set, no MACaddress set

	bootdelay for autoboot 5 sec.
	autoboot disabled


#-----------------------------------
# u-boot FLASH version & RAM version
#-----------------------------------

The u-boot bootloader for Coldfire processors can be configured

	1. as a standalone bootloader residing in flash & relocating itself to RAM on
	startup automatically => "FLASH version"

	2. as a RAM version which will not load from flash automatically as it needs a
	prestage bootloader ("chainloading") & is running only from the RAM address it
	is linked to => "RAM version"

	This version may be very helpful when installing u-boot for the first time
	since it can be used to make available s. th. like a "bootstrap
	mechanism".


How to build the different images:

------------------------------
Flash version
------------------------------

Compile u-boot

in dir ./u-boot-x-x-x/

please first check:

	in ./include/configs/cobra5272.h

		CONFIG_MONITOR_IS_IN_RAM has to be undefined, e. g. as follows:

		#if 0
			#define CONFIG_MONITOR_IS_IN_RAM
			/* define if monitor is started from a pre-loader */
		#endif

	=> u-boot as single bootloader starting from flash


	in board/cobra5272/config.mk CONFIG_SYS_TEXT_BASE should be

		CONFIG_SYS_TEXT_BASE = 0xffe00000

	=> linking address for u-boot as single bootloader stored in flash

then:

	host> make cobra5272_config
		rm -f include/config.h include/config.mk
		Configuring for cobra5272 board...
	host> make
		[...]

	host> cp u-boot.bin /tftpboot/u-boot_flash.bin


------------------------------
RAM version
------------------------------

in dir ./u-boot-x-x-x/

	host> make distclean

please modify the settings:

	in ./include/configs/cobra5272.h

		CONFIG_MONITOR_IS_IN_RAM now has to be defined, e. g. as follows:

		#if 1
			#define CONFIG_MONITOR_IS_IN_RAM
			/*define if monitor is started from a pre-loader */
		#endif

	=> u-boot as RAM version, chainloaded by another bootloader or using bdm cable


	in board/cobra5272/config.mk CONFIG_SYS_TEXT_BASE should be

		CONFIG_SYS_TEXT_BASE = 0x00020000

	=> target linking address for RAM


then:

	host> make cobra5272_config
		rm -f include/config.h include/config.mk
		Configuring for cobra5272 board...
	host> make
		[...]

	host> cp u-boot.bin /tftpboot/u-boot_ram.bin


----
HINT
----

If the m68k-elf-toolchain & the m68k-bdm-gdb is installed you can run the RAM
version by typing (in dir ./u-boot-x-x-x/)
"board/cobra5272/bdm/load-cobra_uboot" ,
in ./u-boot-x-x-x/ the RAM version u-boot (elf format) has to be available.
