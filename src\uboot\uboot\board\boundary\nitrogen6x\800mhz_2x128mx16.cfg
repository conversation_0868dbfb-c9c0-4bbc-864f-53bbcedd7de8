/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (C) 2013 Boundary Devices
 */

DATA 4, MX6_MMDC_P0_MDPDC, 0x0002002D
DATA 4, MX6_MMDC_P0_MDCFG0, 0x40435323
DATA 4, MX6_MMDC_P0_MDCFG1, 0xB66E8D63
DATA 4, MX6_MMDC_P0_MDCFG2, 0x01FF00DB
DATA 4, MX6_MMDC_P0_MDRWD, 0x000026D2
DATA 4, MX6_MMDC_P0_MDOR, 0x00431023
DATA 4, MX6_MMDC_P0_MDOTC, 0x00333030
DATA 4, MX6_MMDC_P0_MDPDC, 0x0002556D
DATA 4, MX6_MMDC_P0_MDASP, 0x00000017
DATA 4, MX6_MMDC_P0_MDCTL, 0x83190000
DATA 4, MX6_<PERSON>MDC_P0_MDSCR, 0x04008032
DATA 4, MX6_MMDC_P0_MDSCR, 0x00008033
DATA 4, MX6_MMDC_P0_MDSCR, 0x00048031
DATA 4, MX6_MMDC_P0_MDSCR, 0x13208030
DATA 4, MX6_MMDC_P0_MDSCR, 0x04008040
DATA 4, MX6_MMDC_P0_MPZQHWCTRL, 0xA1390003
DATA 4, MX6_MMDC_P1_MPZQHWCTRL, 0xA1390003
DATA 4, MX6_MMDC_P0_MDREF, 0x00005800
DATA 4, MX6_MMDC_P0_MPODTCTRL, 0x00022227
DATA 4, MX6_MMDC_P1_MPODTCTRL, 0x00022227
DATA 4, MX6_MMDC_P0_MPDGCTRL0, 0x42350231
DATA 4, MX6_MMDC_P1_MPDGCTRL0, 0x42350231
DATA 4, MX6_MMDC_P0_MPDGCTRL1, 0x021A0218
DATA 4, MX6_MMDC_P1_MPDGCTRL1, 0x021A0218
DATA 4, MX6_MMDC_P0_MPRDDLCTL, 0x4B4B4E49
DATA 4, MX6_MMDC_P1_MPRDDLCTL, 0x4B4B4E49
DATA 4, MX6_MMDC_P0_MPWRDLCTL, 0x3F3F3035
DATA 4, MX6_MMDC_P1_MPWRDLCTL, 0x3F3F3035
DATA 4, MX6_MMDC_P0_MPWLDECTRL0, 0x0040003C
DATA 4, MX6_MMDC_P0_MPWLDECTRL1, 0x0032003E
DATA 4, MX6_MMDC_P1_MPWLDECTRL0, 0x0040003C
DATA 4, MX6_MMDC_P1_MPWLDECTRL1, 0x0032003E
DATA 4, MX6_MMDC_P0_MPMUR0, 0x00000800
DATA 4, MX6_MMDC_P1_MPMUR0, 0x00000800
DATA 4, MX6_MMDC_P0_MDSCR, 0x00000000
DATA 4, MX6_MMDC_P0_MAPSR, 0x00011006
