		Tensilica 'xtfpga' Evaluation Boards
		====================================

Tensilica's 'xtfpga' evaluation boards are actually a set of different
boards that share configurations. The following is a list of supported
hardware by this board type:

- XT-AV60  / LX60
- XT-AV110 / LX110
- XT-AV200 / LX200
- ML605
- KC705

All boards provide the following common configurations:

- An Xtensa or Diamond processor core.
- An on-chip-debug (OCD) JTAG interface.
- A 16550 compatible UART and serial port.
- An OpenCores Wishbone 10/100-base-T ethernet interface.
- A 32 char two line LCD display. (except for the LX200)

LX60/LX110/LX200:

- Virtex-4 (XC4VLX60 / XCV4LX200) / Virtext-5 (XC5VLX110)
- 128MB / 64MB (LX60) memory
- 16MB / 4MB (LX60) Linear Flash

ML605

- Virtex-6 (XC6VLX240T)
- 512MB DDR3 memory
- 16MB Linear BPI Flash

KC705 (Xilinx)

- Kintex-7 XC7K325T FPGA
- 1GB DDR3 memory
- 128MB Linear BPI Flash


Setting up the Board
--------------------

The serial port defaults to 115200 baud, no parity and 1 stop bit.
A terminal emulator must be set accordingly to see the U-Boot prompt.


Board Configurations LX60/LX110/LX200/ML605/KC705
-------------------------------------------------

The LX60/LX110/LX200/ML605 contain an 8-way DIP switch that controls
the boot mapping and selects from a range of default ethernet MAC
addresses.

Boot Mapping (DIP switch 8):

    DIP switch 8 maps the system ROM address space (in which the
    reset vector resides) to either SRAM (off, 0, down) or Flash
    (on, 1, up).  This mapping is implemented in the FPGA bitstream
    and cannot be disabled by software, therefore DIP switch 8 is no
    available for application use. Note DIP switch 7 is reserved by
    Tensilica for future possible hardware use.

    Mapping to SRAM allows U-Boot to be debugged with an OCD/JTAG
    tool such as the Xtensa OCD Daemon connected via a suppored probe.
    See the tools documentation for supported probes and how to
    connect them.  Be aware that the board has only 128 KB of SRAM,
    therefore U-Boot must fit within this space to debug an image
    intended for the Flash.  This issues is discussed in a separate
    section toward the end.

    Mapping to flash allows U-Boot to start on reset, provided it
    has been programmed into the first two 64 KB sectors of the Flash.

    The Flash is always mapped at a device (memory mapped I/O) address
    (the address is board specific and is expressed as CFG_FLASH_BASE).
    The device address is used by U-Boot to program the flash, and may
    be used to specify an application to run or U-Boot image to boot.

Default MAC Address (DIP switches 1-6):

    When the board is first powered on, or after the environment has
    been reinitialized, the ethernet MAC address receives a default
    value whose least significant 6 bits come from DIP switches 1-6.
    The default is 00:50:C2:13:6F:xx where xx ranges from 0..3F
    according to the DIP switches, where "on"==1 and "off"==0, and
    switch 1 is the least-significant bit.

    After initial startup, the MAC address is stored in the U-Boot
    environment variable 'ethaddr'. The user may change this to any
    other address with the "setenv" comamnd. After the environment
    has been saved to Flash by the "saveenv" command, this will be
    used and the DIP switches no longer consulted. DIP swithes 1-6
    may then be used for application purposes.

The KC705 board contains 4-way DIP switch, way 1 is the boot mapping
switch and ways 2-4 control the low three bits of the MAC address.


Limitation of SDRAM Size for OCD Debugging on the LX60
------------------------------------------------------

The XT-AV60 board has only 128 KB of SDRAM that can be mapped
to the system ROM address space for debugging a ROM image under
OCD/JTAG. This limits the useful size of U-Boot to 128 KB (0x20000)
or the first 2 sectors of the flash.

This can pose a problem if all the sources are compiled with -O0
for debugging. The code size is then too large, in which case it
would be necessary to temporarily alter the linker script to place
the load addresses (LMA) in the RAM (VMA) so that OCD loads U-Boot
directly there and does not unpack. In practice this is not really
necessary as long as only a limited set of sources need to be
debugged, because the image can still fit into the 128 KB SRAM.

The recommended procedure for debugging is to first build U-Boot
with the default optimization level (-Os), and then touch and
rebuild incrementally with -O0 so that only the touched sources
are recompiled with -O0. To build with -O0, pass it in the KCFLAGS
variable to make.

Because this problem is easy to fall into and difficult to debug
if one doesn't expect it, the linker script provides a link-time
check and fatal error message if the image size exceeds 128 KB.
