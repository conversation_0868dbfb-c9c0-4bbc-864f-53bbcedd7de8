// SPDX-License-Identifier: GPL-2.0
/*
 * (C) Copyright 2018 Mi<PERSON>
 */

#include <asm/arch/ps7_init_gpl.h>

static unsigned long ps7_pll_init_data_3_0[] = {
	EMIT_MASKWRITE(0xf8000008, 0x0000ffff, 0x0000df0d),
	EMIT_MASKWRITE(0xf8000110, 0x003ffff0, 0x000fa220),
	EMIT_MASKWRITE(0xf8000100, 0x0007f000, 0x00028000),
	EMIT_MASKWRITE(0xf8000100, 0x00000010, 0x00000010),
	EMIT_MASKWRITE(0xf8000100, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf8000100, 0x00000001, 0x00000000),
	EMIT_MASKPOLL(0xf800010c, 0x00000001),
	EMIT_MASKWRITE(0xf8000100, 0x00000010, 0x00000000),
	EMIT_MASKWRITE(0xf8000120, 0x1f003f30, 0x1f000200),
	EMIT_MASKWRITE(0xf8000114, 0x003ffff0, 0x0012c220),
	EMIT_MASKWRITE(0xf8000104, 0x0007f000, 0x00020000),
	EMIT_MASKWRITE(0xf8000104, 0x00000010, 0x00000010),
	EMIT_MASKWRITE(0xf8000104, 0x00000001, 0x000000),
	EMIT_MASKWRITE(0xf8000104, 0x00000001, 0x00000000),
	EMIT_MASKPOLL(0xf800010c, 0x00000002),
	EMIT_MASKWRITE(0xf8000104, 0x00000010, 0x00000000),
	EMIT_MASKWRITE(0xf8000124, 0xfff00003, 0x0c200003),
	EMIT_MASKWRITE(0xf8000118, 0x003ffff0, 0x001452c0),
	EMIT_MASKWRITE(0xf8000108, 0x0007f000, 0x0001e000),
	EMIT_MASKWRITE(0xf8000108, 0x00000010, 0x00000010),
	EMIT_MASKWRITE(0xf8000108, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf8000108, 0x00000001, 0x00000000),
	EMIT_MASKPOLL(0xf800010c, 0x00000004),
	EMIT_MASKWRITE(0xf8000108, 0x00000010, 0x00000000),
	EMIT_MASKWRITE(0xf8000004, 0x0000ffff, 0x0000767b),
	EMIT_EXIT(),
};

static unsigned long ps7_clock_init_data_3_0[] = {
	EMIT_MASKWRITE(0xf8000008, 0x0000ffff, 0x0000df0d),
	EMIT_MASKWRITE(0xf8000128, 0x03f03f01, 0x00302301),
	EMIT_MASKWRITE(0xf8000138, 0x00000011, 0x00000001),
	EMIT_MASKWRITE(0xf8000140, 0x03f03f71, 0x00100801),
	EMIT_MASKWRITE(0xf8000148, 0x00003f31, 0x00000a01),
	EMIT_MASKWRITE(0xf8000150, 0x00003f33, 0x00002801),
	EMIT_MASKWRITE(0xf8000154, 0x00003f33, 0x00001402),
	EMIT_MASKWRITE(0xf8000168, 0x00003f31, 0x00000501),
	EMIT_MASKWRITE(0xf8000170, 0x03f03f30, 0x00101400),
	EMIT_MASKWRITE(0xf8000180, 0x03f03f30, 0x00100a00),
	EMIT_MASKWRITE(0xf8000190, 0x03f03f30, 0x00101e00),
	EMIT_MASKWRITE(0xf80001a0, 0x03f03f30, 0x00101400),
	EMIT_MASKWRITE(0xf80001c4, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf800012c, 0x01ffcccd, 0x016c044d),
	EMIT_MASKWRITE(0xf8000004, 0x0000ffff, 0x0000767b),
	EMIT_EXIT(),
};

static unsigned long ps7_ddr_init_data_3_0[] = {
	EMIT_MASKWRITE(0xf8006000, 0x0001ffff, 0x00000080),
	EMIT_MASKWRITE(0xf8006004, 0x0007ffff, 0x00001081),
	EMIT_MASKWRITE(0xf8006008, 0x03ffffff, 0x03c0780f),
	EMIT_MASKWRITE(0xf800600c, 0x03ffffff, 0x02001001),
	EMIT_MASKWRITE(0xf8006010, 0x03ffffff, 0x00014001),
	EMIT_MASKWRITE(0xf8006014, 0x001fffff, 0x0004281b),
	EMIT_MASKWRITE(0xf8006018, 0xf7ffffff, 0x44e458d1),
	EMIT_MASKWRITE(0xf800601c, 0xffffffff, 0xb2023584),
	EMIT_MASKWRITE(0xf8006020, 0x7fdffffc, 0x2b08b2d0),
	EMIT_MASKWRITE(0xf8006024, 0x0fffffc3, 0x00000000),
	EMIT_MASKWRITE(0xf8006028, 0x00003fff, 0x00002007),
	EMIT_MASKWRITE(0xf800602c, 0xffffffff, 0x00000000),
	EMIT_MASKWRITE(0xf8006030, 0xffffffff, 0x00040970),
	EMIT_MASKWRITE(0xf8006034, 0x13ff3fff, 0x000116d4),
	EMIT_MASKWRITE(0xf8006038, 0x00000003, 0x00000000),
	EMIT_MASKWRITE(0xf800603c, 0x000fffff, 0x00000777),
	EMIT_MASKWRITE(0xf8006040, 0xffffffff, 0xfff00000),
	EMIT_MASKWRITE(0xf8006044, 0x0fffffff, 0x0f666666),
	EMIT_MASKWRITE(0xf8006048, 0x0003f03f, 0x0003c008),
	EMIT_MASKWRITE(0xf8006050, 0xff0f8fff, 0x77010800),
	EMIT_MASKWRITE(0xf8006058, 0x00010000, 0x00000000),
	EMIT_MASKWRITE(0xf800605c, 0x0000ffff, 0x00005003),
	EMIT_MASKWRITE(0xf8006060, 0x000017ff, 0x0000003e),
	EMIT_MASKWRITE(0xf8006064, 0x00021fe0, 0x00020000),
	EMIT_MASKWRITE(0xf8006068, 0x03ffffff, 0x00284545),
	EMIT_MASKWRITE(0xf800606c, 0x0000ffff, 0x00001610),
	EMIT_MASKWRITE(0xf8006078, 0x03ffffff, 0x00466111),
	EMIT_MASKWRITE(0xf800607c, 0x000fffff, 0x00032222),
	EMIT_MASKWRITE(0xf80060a4, 0xffffffff, 0x10200802),
	EMIT_MASKWRITE(0xf80060a8, 0x0fffffff, 0x0690cb73),
	EMIT_MASKWRITE(0xf80060ac, 0x000001ff, 0x000001fe),
	EMIT_MASKWRITE(0xf80060b0, 0x1fffffff, 0x1cffffff),
	EMIT_MASKWRITE(0xf80060b4, 0x00000200, 0x00000200),
	EMIT_MASKWRITE(0xf80060b8, 0x01ffffff, 0x0020006a),
	EMIT_MASKWRITE(0xf80060c4, 0x00000003, 0x00000003),
	EMIT_MASKWRITE(0xf80060c4, 0x00000003, 0x00000000),
	EMIT_MASKWRITE(0xf80060c8, 0x000000ff, 0x00000000),
	EMIT_MASKWRITE(0xf80060dc, 0x00000001, 0x00000000),
	EMIT_MASKWRITE(0xf80060f0, 0x0000ffff, 0x00000000),
	EMIT_MASKWRITE(0xf80060f4, 0x0000000f, 0x00000008),
	EMIT_MASKWRITE(0xf8006114, 0x000000ff, 0x00000000),
	EMIT_MASKWRITE(0xf8006118, 0x7fffffcf, 0x40000001),
	EMIT_MASKWRITE(0xf800611c, 0x7fffffcf, 0x40000001),
	EMIT_MASKWRITE(0xf8006120, 0x7fffffcf, 0x40000001),
	EMIT_MASKWRITE(0xf8006124, 0x7fffffcf, 0x40000001),
	EMIT_MASKWRITE(0xf800612c, 0x000fffff, 0x0002c000),
	EMIT_MASKWRITE(0xf8006130, 0x000fffff, 0x0002c400),
	EMIT_MASKWRITE(0xf8006134, 0x000fffff, 0x0002f003),
	EMIT_MASKWRITE(0xf8006138, 0x000fffff, 0x0002ec03),
	EMIT_MASKWRITE(0xf8006140, 0x000fffff, 0x00000035),
	EMIT_MASKWRITE(0xf8006144, 0x000fffff, 0x00000035),
	EMIT_MASKWRITE(0xf8006148, 0x000fffff, 0x00000035),
	EMIT_MASKWRITE(0xf800614c, 0x000fffff, 0x00000035),
	EMIT_MASKWRITE(0xf8006154, 0x000fffff, 0x00000077),
	EMIT_MASKWRITE(0xf8006158, 0x000fffff, 0x00000077),
	EMIT_MASKWRITE(0xf800615c, 0x000fffff, 0x00000083),
	EMIT_MASKWRITE(0xf8006160, 0x000fffff, 0x00000083),
	EMIT_MASKWRITE(0xf8006168, 0x001fffff, 0x00000105),
	EMIT_MASKWRITE(0xf800616c, 0x001fffff, 0x00000106),
	EMIT_MASKWRITE(0xf8006170, 0x001fffff, 0x00000111),
	EMIT_MASKWRITE(0xf8006174, 0x001fffff, 0x00000110),
	EMIT_MASKWRITE(0xf800617c, 0x000fffff, 0x000000b7),
	EMIT_MASKWRITE(0xf8006180, 0x000fffff, 0x000000b7),
	EMIT_MASKWRITE(0xf8006184, 0x000fffff, 0x000000c3),
	EMIT_MASKWRITE(0xf8006188, 0x000fffff, 0x000000c3),
	EMIT_MASKWRITE(0xf8006190, 0x6ffffefe, 0x00040080),
	EMIT_MASKWRITE(0xf8006194, 0x000fffff, 0x0001fd01),
	EMIT_MASKWRITE(0xf8006204, 0xffffffff, 0x00000000),
	EMIT_MASKWRITE(0xf8006208, 0x000703ff, 0x000003ff),
	EMIT_MASKWRITE(0xf800620c, 0x000703ff, 0x000003ff),
	EMIT_MASKWRITE(0xf8006210, 0x000703ff, 0x000003ff),
	EMIT_MASKWRITE(0xf8006214, 0x000703ff, 0x000003ff),
	EMIT_MASKWRITE(0xf8006218, 0x000f03ff, 0x000003ff),
	EMIT_MASKWRITE(0xf800621c, 0x000f03ff, 0x000003ff),
	EMIT_MASKWRITE(0xf8006220, 0x000f03ff, 0x000003ff),
	EMIT_MASKWRITE(0xf8006224, 0x000f03ff, 0x000003ff),
	EMIT_MASKWRITE(0xf80062a8, 0x00000ff5, 0x00000000),
	EMIT_MASKWRITE(0xf80062ac, 0xffffffff, 0x00000000),
	EMIT_MASKWRITE(0xf80062b0, 0x003fffff, 0x00005125),
	EMIT_MASKWRITE(0xf80062b4, 0x0003ffff, 0x000012a8),
	EMIT_MASKPOLL(0xf8000b74, 0x00002000),
	EMIT_MASKWRITE(0xf8006000, 0x0001ffff, 0x00000081),
	EMIT_MASKPOLL(0xf8006054, 0x00000007),
	EMIT_EXIT(),
};

static unsigned long ps7_mio_init_data_3_0[] = {
	EMIT_MASKWRITE(0xf8000008, 0x0000ffff, 0x0000df0d),
	EMIT_MASKWRITE(0xf8000b40, 0x00000fff, 0x00000600),
	EMIT_MASKWRITE(0xf8000b44, 0x00000fff, 0x00000600),
	EMIT_MASKWRITE(0xf8000b48, 0x00000fff, 0x00000672),
	EMIT_MASKWRITE(0xf8000b4c, 0x00000fff, 0x00000672),
	EMIT_MASKWRITE(0xf8000b50, 0x00000fff, 0x00000674),
	EMIT_MASKWRITE(0xf8000b54, 0x00000fff, 0x00000674),
	EMIT_MASKWRITE(0xf8000b58, 0x00000fff, 0x00000600),
	EMIT_MASKWRITE(0xf8000b5c, 0xffffffff, 0x0018c068),
	EMIT_MASKWRITE(0xf8000b60, 0xffffffff, 0x00f98068),
	EMIT_MASKWRITE(0xf8000b64, 0xffffffff, 0x00f98068),
	EMIT_MASKWRITE(0xf8000b68, 0xffffffff, 0x00f98068),
	EMIT_MASKWRITE(0xf8000b6c, 0x00007fff, 0x00000205),
	EMIT_MASKWRITE(0xf8000b70, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf8000b70, 0x00000021, 0x00000020),
	EMIT_MASKWRITE(0xf8000b70, 0x07feffff, 0x00000823),
	EMIT_MASKWRITE(0xf8000700, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000704, 0x00003fff, 0x00000600),
	EMIT_MASKWRITE(0xf8000708, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf800070c, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000710, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000714, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000718, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf800071c, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000720, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000724, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000728, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf800072c, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000730, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000734, 0x00003fff, 0x00000610),
	EMIT_MASKWRITE(0xf8000738, 0x00003fff, 0x00000611),
	EMIT_MASKWRITE(0xf800073c, 0x00003fff, 0x00000600),
	EMIT_MASKWRITE(0xf8000740, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf8000744, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf8000748, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf800074c, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf8000750, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf8000754, 0x00003fff, 0x00000202),
	EMIT_MASKWRITE(0xf8000758, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf800075c, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf8000760, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf8000764, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf8000768, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf800076c, 0x00003fff, 0x00000203),
	EMIT_MASKWRITE(0xf8000770, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000774, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000778, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf800077c, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000780, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000784, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000788, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf800078c, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000790, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000794, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf8000798, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf800079c, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf80007a0, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007a4, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007a8, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007ac, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007b0, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007b4, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007b8, 0x00003f01, 0x00000201),
	EMIT_MASKWRITE(0xf80007bc, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf80007c0, 0x00003fff, 0x000002e0),
	EMIT_MASKWRITE(0xf80007c4, 0x00003fff, 0x000002e1),
	EMIT_MASKWRITE(0xf80007c8, 0x00003f01, 0x00000201),
	EMIT_MASKWRITE(0xf80007cc, 0x00003fff, 0x00000200),
	EMIT_MASKWRITE(0xf80007d0, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf80007d4, 0x00003fff, 0x00000280),
	EMIT_MASKWRITE(0xf8000830, 0x003f003f, 0x002e0032),
	EMIT_MASKWRITE(0xf8000004, 0x0000ffff, 0x0000767b),
	EMIT_EXIT(),
};

static unsigned long ps7_peripherals_init_data_3_0[] = {
	EMIT_MASKWRITE(0xf8000008, 0x0000ffff, 0x0000df0d),
	EMIT_MASKWRITE(0xf8000b48, 0x00000180, 0x00000180),
	EMIT_MASKWRITE(0xf8000b4c, 0x00000180, 0x00000180),
	EMIT_MASKWRITE(0xf8000b50, 0x00000180, 0x00000180),
	EMIT_MASKWRITE(0xf8000b54, 0x00000180, 0x00000180),
	EMIT_MASKWRITE(0xf8000004, 0x0000ffff, 0x0000767b),
	EMIT_MASKWRITE(0xe0001034, 0x000000ff, 0x00000006),
	EMIT_MASKWRITE(0xe0001018, 0x0000ffff, 0x0000003e),
	EMIT_MASKWRITE(0xe0001000, 0x000001ff, 0x00000017),
	EMIT_MASKWRITE(0xe0001004, 0x000003ff, 0x00000020),
	EMIT_MASKWRITE(0xe000d000, 0x00080000, 0x00080000),
	EMIT_MASKWRITE(0xf8007000, 0x20000000, 0x00000000),
	EMIT_MASKWRITE(0xe000e014, 0x00ffffff, 0x00449144),
	EMIT_MASKWRITE(0xe000e018, 0x00000003, 0x00000000),
	EMIT_MASKWRITE(0xe000e010, 0x03e00000, 0x02400000),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_MASKDELAY(0xf8f00200, 0x00000001),
	EMIT_EXIT(),
};

static unsigned long ps7_post_config_3_0[] = {
	EMIT_MASKWRITE(0xf8000008, 0x0000ffff, 0x0000df0d),
	EMIT_MASKWRITE(0xf8000900, 0x0000000f, 0x0000000f),
	EMIT_MASKWRITE(0xf8000240, 0xffffffff, 0x00000000),
	EMIT_MASKWRITE(0xf8008000, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf8008014, 0x00000001, 0x00000001),
	EMIT_MASKWRITE(0xf8000004, 0x0000ffff, 0x0000767b),
	EMIT_EXIT(),
};

int ps7_init(void)
{
	int ret;

	ret = ps7_config(ps7_mio_init_data_3_0);
	if (ret != PS7_INIT_SUCCESS)
		return ret;
	ret = ps7_config(ps7_pll_init_data_3_0);
	if (ret != PS7_INIT_SUCCESS)
		return ret;
	ret = ps7_config(ps7_clock_init_data_3_0);
	if (ret != PS7_INIT_SUCCESS)
		return ret;
	ret = ps7_config(ps7_ddr_init_data_3_0);
	if (ret != PS7_INIT_SUCCESS)
		return ret;
	ret = ps7_config(ps7_peripherals_init_data_3_0);
	if (ret != PS7_INIT_SUCCESS)
		return ret;

	return PS7_INIT_SUCCESS;
}

int ps7_post_config(void)
{
	return ps7_config(ps7_post_config_3_0);
}
