/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (C) 2013 Boundary Devices
 *
 * Device Configuration Data (DCD)
 *
 * Each entry must have the format:
 * Addr-type           Address        Value
 *
 * where:
 *      Addr-type register length (1,2 or 4 bytes)
 *      Address   absolute address of the register
 *      value     value to be stored in the register
 */

/* set the default clock gate to save power */
DATA 4, CCM_CCGR0, 0x00C03F3F
DATA 4, CCM_CCGR1, 0x0030FC03
DATA 4, CCM_CCGR2, 0x0FFFC000
DATA 4, CCM_CCGR3, 0x3FF00000
DATA 4, CCM_CCGR4, 0x00FFF300
DATA 4, CCM_CCGR5, 0x0F0000C3
DATA 4, CCM_CCGR6, 0x000003FF

/* enable AXI cache for VDOA/VPU/IPU */
DATA 4, MX6_IOMUXC_GPR4, 0xF00000CF
/* set IPU AXI-id0 Qos=0xf(bypass) AXI-id1 Qos=0x7 */
DATA 4, MX6_IOMUXC_GPR6, 0x007F007F
DATA 4, MX6_IOMUXC_GPR7, 0x007F007F

/*
 * Setup CCM_CCOSR register as follows:
 *
 * cko1_en  = 1	   --> CKO1 enabled
 * cko1_div = 111  --> divide by 8
 * cko1_sel = 1011 --> ahb_clk_root
 *
 * This sets CKO1 at ahb_clk_root/8 = 132/8 = 16.5 MHz
 */
DATA 4, CCM_CCOSR, 0x000000fb
