name: CanMV K230 Build for Release

on:
  push:
    tags: [ "v*" ]

jobs:
  build:
    runs-on: ubuntu-24.04
    permissions:
      contents: write
    defaults:
      run:
        shell: bash

    steps:
      - name: Setup SSH
        run: |
          # configure git
          git config --global user.name kendryte747
          git config --global user.email <EMAIL>
          # update ssh key
          mkdir -p ~/.ssh
          echo "${{ secrets.ACTIONS_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

      - name: Install repo
        run: |
          # install repo
          mkdir -p ~/.bin/
          curl https://storage.googleapis.com/git-repo-downloads/repo > ~/.bin/repo
          chmod +x ~/.bin/repo

      - name: Prepare code
        run: |
          rm -rf *
          ls -alh
          ~/.bin/repo init -u **************:canmv-k230/manifest.git
          ~/.bin/repo sync

      # - name: Tag Subprojects
      #   run: |
      #     ./.github/tag_subproject.sh ${{ github.ref_name }}

      - name: Prepare Environment
        run: |
          sudo apt update
          sudo apt install -y bison flex gcc libncurses5-dev pkg-config \
            libconfuse-dev libssl-dev python3 python3-pip python-is-python3 python3-venv \
            cmake libyaml-dev scons mtools bzip2
          python3 -m venv ~/.canmv_venv
          source ~/.canmv_venv/bin/activate
          pip3 install pycryptodome gmssl jsonschema jinja2

      - name: Download toolchains
        run: |
          make dl_toolchain

      - name: Build Projects
        run: |
          source ~/.canmv_venv/bin/activate
          projects=("k230_canmv_01studio_defconfig" "k230_canmv_v1p0p1_defconfig" "k230_canmv_lckfb_defconfig" "k230_canmv_v3p0_defconfig" "k230d_canmv_bpi_zero_defconfig" "k230d_canmv_atk_dnk230d_defconfig" "k230_canmv_dongshanpi_defconfig")
          echo "Config list is ${projects}"
          for proj in ${projects[@]};do
            echo "-------------------"
            echo "build project ${proj}"
            echo "-------------------"
            make ${proj}
            time make log
            echo "-------------------"
          done

      - name: Generate release.xml
        run: |
          ~/.bin/repo manifest -r -o release_${{ github.ref_name }}.xml

      - name: Upload images to Release Asset
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          files: |
            output/**/*.img.gz
            output/**/*.img.gz.md5
            release_${{ github.ref_name }}.xml
          generate_release_notes: true
