# SPDX-License-Identifier: GPL-2.0+
#
# Copyright (C) 2012
# <PERSON> <<EMAIL>>
#
# Based on Kirkwood support:
# (C) Copyright 2009
# Marvell Semiconductor <www.marvell.com>
# Written-by: <PERSON><PERSON><PERSON><PERSON> <prafulla <at> marvell.com>
# Refer doc/README.kwbimage for more details about how-to configure
# and create kirkwood boot image
#

# Boot Media configurations
BOOT_FROM	nand
NAND_ECC_MODE	default
NAND_PAGE_SIZE	0x0800

# SOC registers configuration using bootrom header extension
# Maximum KWBIMAGE_MAX_CONFIG configurations allowed

# Configure RGMII-0 interface pad voltage to 1.8V
DATA 0xffd100e0 0x1b1b1b9b

#Dram initalization for SINGLE x16 CL=5 @ 400MHz
DATA 0xffd01400 0x43000c30	# DDR Configuration register
# bit13-0:  0xc30 (3120 DDR2 clks refresh rate)
# bit23-14: zero
# bit24: 1= enable exit self refresh mode on DDR access
# bit25: 1 required
# bit29-26: zero
# bit31-30: 01

DATA 0xffd01404 0x37543000	# DDR Controller Control Low
# bit 4:    0=addr/cmd in smame cycle
# bit 5:    0=clk is driven during self refresh, we don't care for APX
# bit 6:    0=use recommended falling edge of clk for addr/cmd
# bit14:    0=input buffer always powered up
# bit18:    1=cpu lock transaction enabled
# bit23-20: 5=recommended value for CL=5 and STARTBURST_DEL disabled bit31=0
# bit27-24: 7= CL+2, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
# bit30-28: 3 required
# bit31:    0=no additional STARTBURST delay

DATA 0xffd01408 0x22125451	# DDR Timing (Low) (active cycles value +1)
# bit3-0:   TRAS lsbs
# bit7-4:   TRCD
# bit11- 8: TRP
# bit15-12: TWR
# bit19-16: TWTR
# bit20:    TRAS msb
# bit23-21: 0x0
# bit27-24: TRRD
# bit31-28: TRTP

DATA 0xffd0140c 0x00000a33	#  DDR Timing (High)
# bit6-0:   TRFC
# bit8-7:   TR2R
# bit10-9:  TR2W
# bit12-11: TW2W
# bit31-13: zero required

DATA 0xffd01410 0x000000cc	#  DDR Address Control
# bit1-0:   00, Cs0width=x8
# bit3-2:   11, Cs0size=1Gb
# bit5-4:   00, Cs1width=x8
# bit7-6:   11, Cs1size=1Gb
# bit9-8:   00, Cs2width=nonexistent
# bit11-10: 00, Cs2size =nonexistent
# bit13-12: 00, Cs3width=nonexistent
# bit15-14: 00, Cs3size =nonexistent
# bit16:    0,  Cs0AddrSel
# bit17:    0,  Cs1AddrSel
# bit18:    0,  Cs2AddrSel
# bit19:    0,  Cs3AddrSel
# bit31-20: 0 required

DATA 0xffd01414 0x00000000	#  DDR Open Pages Control
# bit0:    0,  OpenPage enabled
# bit31-1: 0 required

DATA 0xffd01418 0x00000000	#  DDR Operation
# bit3-0:   0x0, DDR cmd
# bit31-4:  0 required

DATA 0xffd0141c 0x00000c52	#  DDR Mode
# bit2-0:   2, BurstLen=2 required
# bit3:     0, BurstType=0 required
# bit6-4:   4, CL=5
# bit7:     0, TestMode=0 normal
# bit8:     0, DLL reset=0 normal
# bit11-9:  6, auto-precharge write recovery ????????????
# bit12:    0, PD must be zero
# bit31-13: 0 required

DATA 0xffd01420 0x00000040	#  DDR Extended Mode
# bit0:    0,  DDR DLL enabled
# bit1:    0,  DDR drive strenght normal
# bit2:    0,  DDR ODT control lsd (disabled)
# bit5-3:  000, required
# bit6:    1,  DDR ODT control msb, (disabled)
# bit9-7:  000, required
# bit10:   0,  differential DQS enabled
# bit11:   0, required
# bit12:   0, DDR output buffer enabled
# bit31-13: 0 required

DATA 0xffd01424 0x0000f17f	#  DDR Controller Control High
# bit2-0:  111, required
# bit3  :  1  , MBUS Burst Chop disabled
# bit6-4:  111, required
# bit7  :  0
# bit8  :  1  , add writepath sample stage, must be 1 for DDR freq >= 300MHz
# bit9  :  0  , no half clock cycle addition to dataout
# bit10 :  0  , 1/4 clock cycle skew enabled for addr/ctl signals
# bit11 :  0  , 1/4 clock cycle skew disabled for write mesh
# bit15-12: 1111 required
# bit31-16: 0    required

DATA 0xffd01428 0x00085520	# DDR2 ODT Read Timing (default values)
DATA 0xffd0147c 0x00008552	# DDR2 ODT Write Timing (default values)

DATA 0xffd01500 0x00000000	# CS[0]n Base address to 0x0
DATA 0xffd01504 0x0ffffff1	# CS[0]n Size
# bit0:    1,  Window enabled
# bit1:    0,  Write Protect disabled
# bit3-2:  00, CS0 hit selected
# bit23-4: ones, required
# bit31-24: 0x0F, Size (i.e. 256MB)

DATA 0xffd01508 0x10000000	# CS[1]n Base address to 256Mb
DATA 0xffd0150c 0x00000000	# CS[2]n Size, window disabled

DATA 0xffd01514 0x00000000	# CS[2]n Size, window disabled
DATA 0xffd0151c 0x00000000	# CS[3]n Size, window disabled

DATA 0xffd01494 0x00030000	#  DDR ODT Control (Low)
# bit3-0:  2, ODT0Rd, MODT[0] asserted during read from DRAM CS1
# bit7-4:  1, ODT0Rd, MODT[0] asserted during read from DRAM CS0
# bit19-16:2, ODT0Wr, MODT[0] asserted during write to DRAM CS1
# bit23-20:1, ODT0Wr, MODT[0] asserted during write to DRAM CS0

DATA 0xffd01498 0x00000000	#  DDR ODT Control (High)
# bit1-0:  00, ODT0 controlled by ODT Control (low) register above
# bit3-2:  01, ODT1 active NEVER!
# bit31-4: zero, required

DATA 0xffd0149c 0x0000e803	# CPU ODT Control
DATA 0xffd01480 0x00000001	# DDR Initialization Control
#bit0=1, enable DDR init upon this register write

# End of Header extension
DATA 0x0 0x0
