/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (C) 2013 Boundary Devices
 */

DATA 4, MX6_MMDC_P0_MDPDC, 0x00020036
DATA 4, MX6_MMDC_P0_MDCFG0, 0x898E7974
DATA 4, MX6_MMDC_P0_MDCFG1, 0xDB538F64
DATA 4, MX6_MMDC_P0_MDCFG2, 0x01FF00DB
DATA 4, MX6_MMDC_P0_MDRWD, 0x000026D2
DATA 4, MX6_MMDC_P0_MDOR, 0x008E1023
DATA 4, MX6_MMDC_P0_MDOTC, 0x09444040
DATA 4, MX6_MMDC_P0_MDPDC, 0x00025576
DATA 4, MX6_MMDC_P0_MDASP, 0x00000047
DATA 4, MX6_MMDC_P0_MDCTL, 0x841A0000
DATA 4, MX6_MMDC_P0_MDSCR, 0x04088032
DATA 4, MX6_MMDC_P0_MDSCR, 0x00008033
DATA 4, MX6_MMDC_P0_MDSCR, 0x00428031
DATA 4, MX6_MMDC_P0_MDSCR, 0x19308030
DATA 4, MX6_MMDC_P0_MDSCR, 0x04008040
DATA 4, MX6_MMDC_P0_MPZQHWCTRL, 0xA1390003
DATA 4, MX6_MMDC_P1_MPZQHWCTRL, 0xA1390003
DATA 4, MX6_MMDC_P0_MDREF, 0x00007800
DATA 4, MX6_MMDC_P0_MPODTCTRL, 0x00022227
DATA 4, MX6_MMDC_P1_MPODTCTRL, 0x00022227
DATA 4, MX6_MMDC_P0_MPDGCTRL0, 0x42740304
DATA 4, MX6_MMDC_P0_MPDGCTRL1, 0x026e0265
DATA 4, MX6_MMDC_P1_MPDGCTRL0, 0x02750306
DATA 4, MX6_MMDC_P1_MPDGCTRL1, 0x02720244
DATA 4, MX6_MMDC_P0_MPRDDLCTL, 0x463d4041
DATA 4, MX6_MMDC_P1_MPRDDLCTL, 0x42413c47
DATA 4, MX6_MMDC_P0_MPWRDLCTL, 0x37414441
DATA 4, MX6_MMDC_P1_MPWRDLCTL, 0x4633473b
DATA 4, MX6_MMDC_P0_MPWLDECTRL0, 0x0025001f
DATA 4, MX6_MMDC_P0_MPWLDECTRL1, 0x00290027
DATA 4, MX6_MMDC_P1_MPWLDECTRL0, 0x001f002b
DATA 4, MX6_MMDC_P1_MPWLDECTRL1, 0x000f0029
DATA 4, MX6_MMDC_P0_MPMUR0, 0x00000800
DATA 4, MX6_MMDC_P1_MPMUR0, 0x00000800
DATA 4, MX6_MMDC_P0_MDSCR, 0x00000000
DATA 4, MX6_MMDC_P0_MAPSR, 0x00011006
