/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (C) 2013 Boundary Devices
 *
 * Device Configuration Data (DCD)
 *
 * Each entry must have the format:
 * Addr-type           Address        Value
 *
 * where:
 *      Addr-type register length (1,2 or 4 bytes)
 *      Address   absolute address of the register
 *      value     value to be stored in the register
 */

/*
 * DDR3 settings
 * MX6Q    ddr is limited to 1066 Mhz	currently 1056 MHz(528 MHz clock),
 *	   memory bus width: 64 bits	x16/x32/x64
 * MX6DL   ddr is limited to 800 MHz(400 MHz clock)
 *	   memory bus width: 64 bits	x16/x32/x64
 * MX6SOLO ddr is limited to 800 MHz(400 MHz clock)
 *	   memory bus width: 32 bits	x16/x32
 */
DATA 4, MX6_IOM_DRAM_SDQS0, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS1, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS2, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS3, 0x00000030
DATA 4, MX6_I<PERSON>_DRAM_SDQS4, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS5, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS6, 0x00000030
DATA 4, MX6_IOM_DRAM_SDQS7, 0x00000030

DATA 4, MX6_IOM_GRP_B0DS, 0x00000030
DATA 4, MX6_IOM_GRP_B1DS, 0x00000030
DATA 4, MX6_IOM_GRP_B2DS, 0x00000030
DATA 4, MX6_IOM_GRP_B3DS, 0x00000030
DATA 4, MX6_IOM_GRP_B4DS, 0x00000030
DATA 4, MX6_IOM_GRP_B5DS, 0x00000030
DATA 4, MX6_IOM_GRP_B6DS, 0x00000030
DATA 4, MX6_IOM_GRP_B7DS, 0x00000030
DATA 4, MX6_IOM_GRP_ADDDS, 0x00000030
/* 40 Ohm drive strength for cs0/1,sdba2,cke0/1,sdwe */
DATA 4, MX6_IOM_GRP_CTLDS, 0x00000030

DATA 4, MX6_IOM_DRAM_DQM0, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM1, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM2, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM3, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM4, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM5, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM6, 0x00020030
DATA 4, MX6_IOM_DRAM_DQM7, 0x00020030

DATA 4, MX6_IOM_DRAM_CAS, 0x00020030
DATA 4, MX6_IOM_DRAM_RAS, 0x00020030
DATA 4, MX6_IOM_DRAM_SDCLK_0, 0x00020030
DATA 4, MX6_IOM_DRAM_SDCLK_1, 0x00020030

DATA 4, MX6_IOM_DRAM_RESET, 0x00020030
DATA 4, MX6_IOM_DRAM_SDCKE0, 0x00003000
DATA 4, MX6_IOM_DRAM_SDCKE1, 0x00003000

DATA 4, MX6_IOM_DRAM_SDODT0, 0x00003030
DATA 4, MX6_IOM_DRAM_SDODT1, 0x00003030

/* (differential input) */
DATA 4, MX6_IOM_DDRMODE_CTL, 0x00020000
/* (differential input) */
DATA 4, MX6_IOM_GRP_DDRMODE, 0x00020000
/* disable ddr pullups */
DATA 4, MX6_IOM_GRP_DDRPKE, 0x00000000
DATA 4, MX6_IOM_DRAM_SDBA2, 0x00000000
/* 40 Ohm drive strength for cs0/1,sdba2,cke0/1,sdwe */
DATA 4, MX6_IOM_GRP_DDR_TYPE, 0x000C0000

/* Read data DQ Byte0-3 delay */
DATA 4, MX6_MMDC_P0_MPRDDQBY0DL, 0x33333333
DATA 4, MX6_MMDC_P0_MPRDDQBY1DL, 0x33333333
DATA 4, MX6_MMDC_P0_MPRDDQBY2DL, 0x33333333
DATA 4, MX6_MMDC_P0_MPRDDQBY3DL, 0x33333333
DATA 4, MX6_MMDC_P1_MPRDDQBY0DL, 0x33333333
DATA 4, MX6_MMDC_P1_MPRDDQBY1DL, 0x33333333
DATA 4, MX6_MMDC_P1_MPRDDQBY2DL, 0x33333333
DATA 4, MX6_MMDC_P1_MPRDDQBY3DL, 0x33333333

/*
 * MDMISC	mirroring	interleaved (row/bank/col)
 */
DATA 4, MX6_MMDC_P0_MDMISC, 0x00081740

/*
 * MDSCR	con_req
 */
DATA 4, MX6_MMDC_P0_MDSCR, 0x00008000
