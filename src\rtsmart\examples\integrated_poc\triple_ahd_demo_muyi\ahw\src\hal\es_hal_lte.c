#include "es_inc.h"

#define ES_HAL_LTE_DEBUG
#ifdef ES_HAL_LTE_DEBUG
#define es_hal_lte_debug es_log_info
#define es_hal_lte_error es_log_error
#else
#define es_hal_lte_debug(...)
#define es_hal_lte_error(...)
#endif

#define GPS_L76K_ENABLE             (1)

#define LTE_AT_DEV_NAME             ("/dev/ttyUSB2")
#if GPS_L76K_ENABLE
#define GPS_AT_DEV_NAME             ("/dev/uart1")
#define GPS_L76K_RESET_PIN          (42)
#endif
#define LTE_GPS_CACHE_FILE          ("/sdcard/gps.txt")
#if GPS_L76K_ENABLE
#define LTE_AT_RESP_STR_LEN         (1024)
#else
#define LTE_AT_RESP_STR_LEN         (256)
#endif
#define LTE_GPS_STR_LEN             (128)

static ES_CHAR hal_lte_imei[ES_LTE_IMEI_LEN+1] = {0};
static ES_CHAR hal_lte_iccid[ES_LTE_ICCID_LEN+1] = {0};
static ES_CHAR gps_info_cache[LTE_GPS_STR_LEN] = {0};
static ES_CHAR gps_rx_data[LTE_AT_RESP_STR_LEN] = {0};
static ES_U8 hal_lte_rssi = 0;
static ES_U8 hal_lte_ate0_init = 0;
static ES_U8 hal_lte_dialup_init = 0;
static ES_U8 hal_lte_apn_init = 0;
#if GPS_L76K_ENABLE
static es_gps_info_t l76k_gps_info = {0};
static ES_BOOL got_gps_data = ES_FALSE;
#endif

// 添加互斥锁来保护LTE UART访问
static pthread_mutex_t lte_uart_mutex = PTHREAD_MUTEX_INITIALIZER;


static ES_VOID lte_gps_load(ES_VOID)
{
    ES_S32 fd = 0;
    ES_S32 len = 0;

    if (0 != access(LTE_GPS_CACHE_FILE, F_OK)) {
        return;
    }

    fd = open(LTE_GPS_CACHE_FILE, O_RDONLY);
    if (-1 == fd) {
        return;
    }

    lseek(fd, 0, SEEK_SET);
    read(fd, (void *)(gps_info_cache), LTE_GPS_STR_LEN);
    close(fd);
}

static ES_S32 lte_gps_save(char *gps_str)
{
    ES_S32 i;
    ES_CHAR sys_cmd_str[256] = {0};
    static ES_U32 count = 0;
    ES_S32 fd;
    ES_S32 write_len = 0;

    for (i = 0; i < LTE_GPS_STR_LEN; i++) {
        if ('\r' == gps_str[i] || '\n' == gps_str[i]) {
            gps_str[i] = 0;
            break;
        }
    }

    memset(gps_info_cache, 0x00, sizeof(gps_info_cache));
    strncpy(gps_info_cache, gps_str, LTE_GPS_STR_LEN);

    if (count > 0 && count < 10) {
        count++;
        return ES_RET_SUCCESS;
    }

    // save 
    fd = open(LTE_GPS_CACHE_FILE, O_WRONLY | O_CREAT);
    if (-1 == fd) {
        return;
    }

    write_len = strlen(gps_info_cache);
    write(fd, gps_info_cache, write_len);
    close(fd);
    sync();

    return ES_RET_SUCCESS;
}

static ES_S32 es_hal_lte_uart_send(const char *at, char *at_resp)
{
    ES_S32 fd = -1;
    ES_S32 ret = ES_RET_SUCCESS;
    int n_read = 0;
    int total_read = 0;
    int retry_count = 0;
    const int max_retries = 10; // 最多重试10次，总共约3秒

    // 获取互斥锁，防止多线程同时访问LTE UART
    pthread_mutex_lock(&lte_uart_mutex);

    fd = open(LTE_AT_DEV_NAME, O_RDWR|O_NONBLOCK|O_NOCTTY|O_SYNC);
    if (fd < 0){
        es_hal_lte_error("open uart err(open %s).", LTE_AT_DEV_NAME);
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }

    es_hal_lte_debug("write:%s", at);
    if (write(fd, at, strlen(at)) < 0) {
        es_hal_lte_error("uart write fail");
        ret = ES_RET_FAILURE;
        goto FUNC_END;
    }
    fsync(fd);

    // 清空响应缓冲区
    memset(at_resp, 0, LTE_AT_RESP_STR_LEN);

    usleep(300*1000);

    // 循环读取响应，直到收到完整响应或超时
    do{
        n_read = read(fd, at_resp + total_read, LTE_AT_RESP_STR_LEN - total_read - 1);
        if(n_read > 0) {
            total_read += n_read;
            at_resp[total_read] = '\0'; // 确保字符串以空字符结尾

            // 检查响应是否完整
            if (strstr(at_resp, "OK") || strstr(at_resp, "ERROR") ||
                strstr(at_resp, "+CME ERROR:") || strstr(at_resp, "+CMS ERROR:")) {
                es_hal_lte_debug("got complete response");
                break;
            }
        } else {
            es_hal_lte_debug("at no resp, retry %d/%d", retry_count, max_retries);
            retry_count++;
            if (retry_count >= max_retries) {
                es_hal_lte_debug("max retries reached");
                break;
            }
        }
        usleep(300*1000); // 每次重试等待300ms
    } while(1);
    es_hal_lte_debug("at resp(total_read=%d):%s", total_read, at_resp);

FUNC_END:
    if (fd >= 0) {
        close(fd);
    }

    // 释放互斥锁
    pthread_mutex_unlock(&lte_uart_mutex);

    return ret;
}


#if GPS_L76K_ENABLE
static ES_S32 hal_lte_gps_init(ES_VOID)
{
    drv_gpio_inst_t* gpio = NULL;
    int ret;

    // 准备GPIO
    drv_fpioa_set_pin_func(GPS_L76K_RESET_PIN, GPIO0 + GPS_L76K_RESET_PIN);
    ret = drv_gpio_inst_create(GPS_L76K_RESET_PIN, &gpio);
    if (0 != ret) {
        es_hal_lte_error("L76K gpio create fail");
        return ES_RET_FAILURE;
    }

    // 设置为输出模式
    ret = drv_gpio_mode_set(gpio, GPIO_DM_OUTPUT);
    if (0 != ret) {
        es_hal_lte_error("L76K gpio set mode fail");
        return ES_RET_FAILURE;
    }

    // 输出高电平
    ret = drv_gpio_value_set(gpio, GPIO_PV_HIGH);
    if (0 != ret) {
        es_hal_lte_error("L76K gpio set value fail");
        return ES_RET_FAILURE;
    }
    es_hal_lte_debug("L76K gpio init success");

    return ES_RET_SUCCESS;
}

static ES_S32 gps_l76k_minmea_parse(const ES_CHAR *gps_minmea_str, es_gps_info_t *gps_info)
{
    const ES_CHAR *minmea_str = ES_NULL;

    // gga
    minmea_str = strstr(gps_minmea_str, "$GNGGA");
    if (ES_NULL == minmea_str) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_gps_minmea_parse((const ES_CHAR *)(minmea_str), (es_gps_info_t *)gps_info)) {
        es_hal_lte_error("gps gga parse fail(%s)", gps_minmea_str);
        return ES_RET_FAILURE;
    }
    got_gps_data = ES_TRUE;

    // rmc
    minmea_str = strstr(gps_minmea_str, "$GNRMC");
    if (ES_NULL == minmea_str) {
        return ES_RET_SUCCESS;
    }
     if (ES_RET_SUCCESS != es_gps_minmea_parse((const ES_CHAR *)(minmea_str), (es_gps_info_t *)gps_info)) {
        es_hal_lte_error("gps rmc parse fail(%s)", gps_minmea_str);
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_VOID *l76k_read_task(ES_VOID *args)
{
    ES_S32 fd = -1;
    int total_read_len = 0;
    int n_read = 0;
    char *gga_str_ptr = ES_NULL;
    char *zda_str_prt = ES_NULL;
    fd_set readfds;
    struct timeval tv;
    int retval;

    (void)args;

    fd = open(GPS_AT_DEV_NAME, O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd < 0){
        es_hal_lte_error("open uart err(open %s).", GPS_AT_DEV_NAME);
        return ES_NULL;
    }
    // lte_set_uart_param(fd, B115200);

    // 设置非阻塞模式
    fcntl(fd, F_SETFL, FNDELAY);

    while(1) {
        sleep(1);

        if (total_read_len >= LTE_AT_RESP_STR_LEN) {
            total_read_len = 0;
            memset(gps_rx_data, 0x00, sizeof(gps_rx_data));
        }

        FD_ZERO(&readfds);
        FD_SET(fd, &readfds);
        tv.tv_sec = 1;
        tv.tv_usec = 0;

        retval = select(fd + 1, &readfds, NULL, NULL, &tv);
        if (retval < 0) {
            es_hal_lte_error("select error: %s", strerror(errno));
            continue;
        } else if (retval == 0) {
            // 超时，继续等待
            continue;
        }

        if (0 == FD_ISSET(fd, &readfds)) {
            es_hal_lte_error("Timeout occurred, no data after 1 second.\n");
            continue;
        }

        n_read = read(fd, (void *)(gps_rx_data+total_read_len), (LTE_AT_RESP_STR_LEN-1)-total_read_len);
        if (n_read <= 0) {
            es_hal_lte_error("gps read no data, n_read=%d, total_read_len=%d", n_read, total_read_len);
            if (total_read_len >= (LTE_AT_RESP_STR_LEN-1)) {
                total_read_len = 0;
                memset(gps_rx_data, 0x00, sizeof(gps_rx_data));
            }
            continue;
        }
        total_read_len += n_read;

        int from_cache = 0;
        if (strstr(gps_rx_data, "$GNGLL,,,,,,") || strstr(gps_rx_data, ",,,,,,,,,,,,,") ) {
            es_hal_lte_error("gps data all null");
            es_hal_lte_error("gps_rx_data=%s", gps_rx_data);
            total_read_len = 0;
            memset(gps_rx_data, 0x00, sizeof(gps_rx_data));
            from_cache = 1;
            if (0 == gps_info_cache[0]) {
                es_hal_lte_error("gps not cache info");
            } else {
                gga_str_ptr = gps_info_cache;
                gps_l76k_minmea_parse(gga_str_ptr, &l76k_gps_info);
                es_strncpy(l76k_gps_info.spkm, "0.0", sizeof(l76k_gps_info.spkm));
            }
            continue;
        }

        gga_str_ptr = strstr(gps_rx_data, "$GNGGA");
        if (ES_NULL == gga_str_ptr) {
            continue;
        }

        zda_str_prt = strstr(gps_rx_data, "$GNZDA");
        if (ES_NULL == zda_str_prt) {
            continue;
        }

        es_hal_lte_debug("gps data:%s", gps_rx_data);
        gps_l76k_minmea_parse(gga_str_ptr, &l76k_gps_info);
        lte_gps_save(gga_str_ptr);
        total_read_len = 0;
        memset(gps_rx_data, 0x00, sizeof(gps_rx_data));
    }

    if (fd >= 0) {
        close(fd);
    }
    return ES_NULL;
}
#endif

ES_S32 es_hal_lte_init(ES_VOID)
{
#if GPS_L76K_ENABLE
    pthread_t l76k_thead;

    memset(&l76k_gps_info, 0x00, sizeof(l76k_gps_info));
    lte_gps_load();
    hal_lte_gps_init();
    pthread_create(&l76k_thead, NULL, l76k_read_task, NULL);
#endif

    memset(hal_lte_imei, 0x00, sizeof(hal_lte_imei));

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_exit(ES_VOID)
{
    return ES_RET_SUCCESS;
}

static char* lte_read_gps_by_at(char *at_resp)
{
    char *gps_loc_str = ES_NULL;

    if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+QGPSLOC=2\r\n", at_resp)) {
        es_hal_lte_error("get gps info fail");
        return ES_NULL;
    }

#if 0
    // for test
    memset(at_resp, 0x00, sizeof(at_resp));
    strcpy(at_resp, "+QGPSLOC: 112634.00,22.64268,114.03305,99.99,56.6,3,,1.214,0.665,290624,06");
#endif
    if (0 == at_resp[0]) {
        es_hal_lte_error("get gps, not resp");
        return ES_NULL;
    }

    gps_loc_str = strstr(at_resp, "+QGPSLOC");
    if (ES_NULL == gps_loc_str) {
        es_hal_lte_error("no \"+QGPSLOC\"");
        return ES_NULL;
    }

    return gps_loc_str;
}

// es_gps_info_t
ES_S32 es_hal_lte_get_gps(ES_VOID *gps)
{
#if GPS_L76K_ENABLE
    if (!got_gps_data) {
        return ES_RET_FAILURE;
    }

    memcpy(gps, &l76k_gps_info, sizeof(l76k_gps_info));
    return ES_RET_SUCCESS;
#else
    char *gps_minmea_str = ES_NULL;
    ES_BOOL from_cache = 0;
    es_gps_info_t *gps_info;

    gps_minmea_str = lte_read_gps_by_at(gps_rx_data);
    if (ES_NULL == gps_minmea_str) {
        from_cache = 1;
        memset(gps_rx_data, 0x00, LTE_AT_RESP_STR_LEN);
        if (0 == gps_info_cache[0]) {
            es_hal_lte_error("gps not cache info");
            return ES_RET_FAILURE;
        }
        gps_minmea_str = gps_info_cache;
    }


    gps_info = (es_gps_info_t *)gps;
    if (ES_RET_SUCCESS != es_gps_minmea_parse((const ES_CHAR *)(gps_minmea_str+3), (es_gps_info_t *)gps_info)) {
        es_hal_lte_error("gps parse fail(%s)", gps_minmea_str);
        return ES_RET_FAILURE;
    }

    if (0 == from_cache) {
        lte_gps_save(gps_minmea_str);
    } else {
        memset(gps_info->spkm, 0X00, sizeof(gps_info->spkm));
        strcpy(gps_info->spkm, "0.0");
        memset(gps_info->spkn, 0X00, sizeof(gps_info->spkn));
        strcpy(gps_info->spkn, "0.0");
    }
#endif
    return ES_RET_SUCCESS;
}

// AT+GSN
// 860503074192503
ES_S32 es_hal_lte_get_mac(ES_BYTE *mac)
{
    char at_resp[LTE_AT_RESP_STR_LEN] = {0};
    ES_U8 i = 0;
    ES_U8 j = 0;
    char *imei_str = ES_NULL;

    if (0 == hal_lte_ate0_init) {
        if (ES_RET_SUCCESS != es_hal_lte_uart_send("ATE0\r\n", at_resp)) {
            es_hal_lte_error("send ATE0 fail");
            return ES_RET_FAILURE;
        }
        hal_lte_ate0_init = 1;
    }

    /* 初始化拨号  */
    if (0 == hal_lte_dialup_init) {
        if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+MDIALUPCFG=\"auto\",1\r\n", at_resp)) {
            es_hal_lte_error("send MDIALUPCFG fail");
            return ES_RET_FAILURE;
        }
        hal_lte_dialup_init = 1;
    }



    if (0 == hal_lte_imei[0]) {
        if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+GSN=1\r\n", at_resp)) {
        // if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+CSQ\r\n", at_resp)) {
            es_hal_lte_error("get imei fail");
            return ES_RET_FAILURE;
        }

        imei_str = strstr(at_resp,"86");
        if (ES_NULL == imei_str) {
            es_hal_lte_error("get imei fail, not start with 86");
            return ES_RET_FAILURE;
        }

        if (!es_isxdigit(imei_str[0]) || !es_isxdigit(imei_str[ES_LTE_IMEI_LEN-1])) {
            es_hal_lte_error("get imei fail, not isxdigit");
            return ES_RET_FAILURE;
        }

        strncpy(hal_lte_imei, imei_str, ES_LTE_IMEI_LEN);
    }

    for (i = 0; i < 6; i++) {
        // just last 12 char, so start with 3.
        j = 3+i*2;
        mac[i] = (((hal_lte_imei[j]-'0')<<4)&0xF0) | ((hal_lte_imei[j+1]-'0')&0x0F);
    }

    if (0 == hal_lte_apn_init) {
        es_hal_lte_uart_send("AT+QICSGP=1,1,\"CTNET\",\"\",\"\",1\r\n", at_resp);
        es_hal_lte_uart_send("AT+QIACT=1\r\n", at_resp);
        hal_lte_apn_init = 1;
    }

    return ES_RET_SUCCESS;
}

// AT+QCCID
// +QCCID: 898608111823D0322387
const ES_CHAR *es_hal_lte_get_iccid(ES_VOID)
{
    char at_resp[LTE_AT_RESP_STR_LEN] = {0};
    ES_U8 i = 0;
    ES_U8 j = 0;
    char *val_str = ES_NULL;

    if (0 == hal_lte_iccid[0]) {
        if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+QCCID\r\n", at_resp)) {
            es_hal_lte_error("get iccid fail");
            return ES_NULL;
        }

        val_str = strstr(at_resp,"+QCCID: ");
        if (ES_NULL == val_str) {
            es_hal_lte_error("get iccd fail, no +QCCID");
            return ES_NULL;
        }

        strncpy(hal_lte_iccid, (char*)(val_str+8), ES_LTE_ICCID_LEN);
    }

    return hal_lte_iccid;
}

// AT+CSQ
// +CSQ: 26,99
ES_S8 es_hal_lte_get_rssi(ES_VOID)
{
    char at_resp[LTE_AT_RESP_STR_LEN] = {0};
    char *val_str = ES_NULL;

    if (0 == hal_lte_rssi) {
        if (ES_RET_SUCCESS != es_hal_lte_uart_send("AT+CSQ\r\n", at_resp)) {
            es_hal_lte_error("get rssi fail");
            return 0;
        }

        val_str = strstr(at_resp,"+CSQ: ");
        if (ES_NULL == val_str) {
            es_hal_lte_error("get rssi fail, no +CSQ");
            return 0;
        }

        hal_lte_rssi = (ES_S8)es_atoi((const ES_CHAR *)(val_str+6));
    }

    return hal_lte_rssi;
}

