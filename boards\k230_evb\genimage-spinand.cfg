flash spinand-128m {
	uffs = true
	page-size = 2048
	block-pages = 64
	total-blocks = 1024
	spare-size = 64
	status-offset = 0
}

image  bin.uffs {
	uffs {
		size = 10M
	}
	size = 11M
	mountpoint = "bin"
	flashtype = "spinand-128m"
}

image  sdcard.uffs {
	uffs {
		size = 50M
	}
	size = 60M
	mountpoint = "sdcard"
	flashtype = "spinand-128m"
}

image sysimage-spinand.kdimg {
	kdimage {
		image_info = "v1.1.0"
		chip_info = "K230"
		board_info = "K230_evb_spi_nand"
	}

	# partition loader {
	# 	in-partition-table = false
	# 	offset = 0
	# 	image = "/home/<USER>/work/rtos_k230/src/uboot/uboot/u-boot.bin"
	# }

	partition spl_a {
		in-partition-table = false
		offset = 0
		image = "uboot/swap_fn_u-boot-spl.bin"
	}

	partition spl_b {
		in-partition-table = false
		offset = 512K
		image = "uboot/swap_fn_u-boot-spl.bin"
	}

	partition uboot {
		in-partition-table = false
		offset = 2M
		size = 2M
		image = "uboot/fn_ug_u-boot.bin"
	}

	partition uboot_env {
		in-partition-table = false
		offset = 4M
		size = 512K
		image = "uboot/env.bin"
	}

    partition rtt {
		in-partition-table = false
		offset = 5M
		size = 20M
		image = "opensbi/opensbi_rtt_system.bin"
	}

    partition bin {
		in-partition-table = false
		flag = 0x400000008000040 # write_with_oob, page size 2048, oob size 64
		offset = 40M
		image = "bin.uffs"
	}

    partition sdcard {
		in-partition-table = false
		flag = 0x400000008000040 # write_with_oob, page size 2048, oob size 64
		offset = 60M
		size = 60M
		image = "sdcard.uffs"
	}
}
