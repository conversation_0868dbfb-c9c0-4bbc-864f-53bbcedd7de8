# name: CanMV K230 Build and Test

# on:
#   push:
#     tags: [ "v*" ]
#     branches: [ "dev", "test/*" ]
#   pull_request:
#     branches: [ "main" ]

# jobs:
#   build:
#     runs-on: self-hosted
#     permissions:
#       contents: write
#     defaults:
#       run:
#         shell: bash

#     steps:
#       - name: Setup SSH
#         run: |
#           # configure git
#           git config --global user.name kendryte747
#           git config --global user.email <EMAIL>
#           # update ssh key
#           mkdir -p ~/.ssh
#           echo "${{ secrets.ACTIONS_PRIVATE_KEY }}" > ~/.ssh/id_rsa
#           chmod 600 ~/.ssh/id_rsa

#       - name: Install repo
#         run: |
#           # install repo
#           mkdir -p ~/bin/
#           curl https://storage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
#           chmod +x ~/bin/repo

#       - name: Prepare code
#         run: |
#           rm -rf *
#           ls -alh
#           ~/bin/repo init -u **************:canmv-k230/manifest.git
#           ~/bin/repo sync

#       - name: Prepare Environment
#         run: |
#           sudo apt update
#           sudo apt install -y bison flex gcc libncurses5-dev pkg-config \
#             libconfuse-dev libssl-dev python3 python3-pip python-is-python3 \
#             cmake libyaml-dev scons mtools bzip2
#           pip3 install pycryptodome gmssl

#       - name: Download toolchains
#         run: |
#           make dl_toolchain

#       - name: Build Projects
#         run: |
#           projects=("k230_canmv_01studio_defconfig")
#           echo "Config list is ${projects}"
#           for proj in ${projects[@]};do
#             echo "-------------------"
#             echo "build project ${proj}"
#             echo "-------------------"
#             make ${proj}
#             time make log
#             echo "-------------------"
#           done
