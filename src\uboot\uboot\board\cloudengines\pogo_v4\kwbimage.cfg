# SPDX-License-Identifier: GPL-2.0+
#
# Copyright (C) 2012
# <PERSON> <<EMAIL>>
#
# Based on Kirkwood support:
# (C) Copyright 2009
# Marvell Semiconductor <www.marvell.com>
# Written-by: <PERSON><PERSON><PERSON><PERSON> <prafulla <at> marvell.com>

# Boot Media configurations   (DONE)
BOOT_FROM	nand
NAND_ECC_MODE	default
NAND_PAGE_SIZE	0x0800

# SOC registers configuration using bootrom header extension
# Maximum KWBIMAGE_MAX_CONFIG configurations allowed

# Configure RGMII-0 interface pad voltage to 1.8V (SHOULD BE SAME)
DATA 0xffd100e0 0x1b1b1b9b

#Dram initalization for SINGLE x16 CL=3 @ 200MHz   (need CL=3 @ 200MHz?)
DATA 0xffd01400 0x43000618	# DDR Configuration register
# bit13-0:  0x200 (200 DDR2 clks refresh rate)
# bit23-14: zero
# bit24: 1= enable exit self refresh mode on DDR access
# bit25: 1 required
# bit29-26: zero
# bit31-30: 01

DATA 0xffd01404 0x34143000	# DDR Controller Control Low
# bit 4:    0=addr/cmd in smame cycle
# bit 5:    0=clk is driven during self refresh, we don't care for APX
# bit 6:    0=use recommended falling edge of clk for addr/cmd
# bit14:    0=input buffer always powered up
# bit18:    1=cpu lock transaction enabled
# bit23-20: 3=recommended value for CL=3 and STARTBURST_DEL disabled bit31=0
# bit27-24: 6= CL+3, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
# bit30-28: 3 required
# bit31:    0=no additional STARTBURST delay

DATA 0xffd01408 0x11012227	# DDR Timing (Low) (active cycles value +1)
# bit3-0:   TRAS lsbs
# bit7-4:   TRCD
# bit11- 8: TRP
# bit15-12: TWR
# bit19-16: TWTR
# bit20:    TRAS msb
# bit23-21: 0x0
# bit27-24: TRRD
# bit31-28: TRTP

DATA 0xffd0140c 0x00000819	#  DDR Timing (High)
# bit6-0:   TRFC
# bit8-7:   TR2R
# bit10-9:  TR2W
# bit12-11: TW2W
# bit31-13: zero required

DATA 0xffd01410 0x00000001	#  DDR Address Control  (changed to Dockstar vals)
# bit1-0:   00, Cs0width=x16
# bit3-2:   10, Cs0size=512Mb
# bit5-4:   00, Cs2width=nonexistent
# bit7-6:   00, Cs1size =nonexistent
# bit9-8:   00, Cs2width=nonexistent
# bit11-10: 00, Cs2size =nonexistent
# bit13-12: 00, Cs3width=nonexistent
# bit15-14: 00, Cs3size =nonexistent
# bit16:    0,  Cs0AddrSel
# bit17:    0,  Cs1AddrSel
# bit18:    0,  Cs2AddrSel
# bit19:    0,  Cs3AddrSel
# bit31-20: 0 required

DATA 0xffd01414 0x00000000	#  DDR Open Pages Control
# bit0:    0,  OpenPage enabled
# bit31-1: 0 required

DATA 0xffd01418 0x00000000	#  DDR Operation
# bit3-0:   0x0, DDR cmd
# bit31-4:  0 required

DATA 0xffd0141c 0x00000632	#  DDR Mode
# bit2-0:   2, BurstLen=2 required
# bit3:     0, BurstType=0 required
# bit6-4:   4, CL=5				(<===== change to CL=3 ?)
# bit7:     0, TestMode=0 normal
# bit8:     0, DLL reset=0 normal
# bit11-9:  6, auto-precharge write recovery ????????????
# bit12:    0, PD must be zero
# bit31-13: 0 required

DATA 0xffd01420 0x00000040	#  DDR Extended Mode
# bit0:    0,  DDR DLL enabled
# bit1:    0,  DDR drive strenght normal
# bit2:    0,  DDR ODT control lsd (disabled)
# bit5-3:  000, required
# bit6:    1,  DDR ODT control msb, (disabled)
# bit9-7:  000, required
# bit10:   0,  differential DQS enabled
# bit11:   0, required
# bit12:   0, DDR output buffer enabled
# bit31-13: 0 required

DATA 0xffd01424 0x0000F07F	#  DDR Controller Control High
# bit2-0:  111, required
# bit3  :  1  , MBUS Burst Chop disabled
# bit6-4:  111, required
# bit7  :  0
# bit8  :  0  , no sample stage
# bit9  :  0  , no half clock cycle addition to dataout
# bit10 :  0  , 1/4 clock cycle skew enabled for addr/ctl signals
# bit11 :  0  , 1/4 clock cycle skew disabled for write mesh
# bit15-12: 1111 required
# bit31-16: 0    required

DATA 0xffd01428 0x00085520	# DDR2 ODT Read Timing (default values)
DATA 0xffd0147c 0x00008552	# DDR2 ODT Write Timing (default values)

DATA 0xFFD01500 0x00000000	# CS[0]n Base address to 0x0
DATA 0xFFD01504 0x07FFFFF1	# CS[0]n Size
# bit0:    1,  Window enabled
# bit1:    0,  Write Protect disabled
# bit3-2:  00, CS0 hit selected
# bit23-4: ones, required
# bit31-24: 0x07, Size (i.e. 128MB)

DATA 0xFFD0150C 0x00000000	# CS[1]n Size, window disabled
DATA 0xFFD01514 0x00000000	# CS[2]n Size, window disabled
DATA 0xFFD0151C 0x00000000	# CS[3]n Size, window disabled

DATA 0xffd01494 0x00030000	#  DDR ODT Control (Low)		 (DONE)
# bit3-0:  2, ODT0Rd, MODT[0] asserted during read from DRAM CS1
# bit7-4:  1, ODT0Rd, MODT[0] asserted during read from DRAM CS0
# bit19-16:2, ODT0Wr, MODT[0] asserted during write to DRAM CS1
# bit23-20:1, ODT0Wr, MODT[0] asserted during write to DRAM CS0

DATA 0xffd01498 0x00000000	#  DDR ODT Control (High)  (DONE)
# bit1-0:  00, ODT0 controlled by ODT Control (low) register above
# bit3-2:  01, ODT1 active NEVER!
# bit31-4: zero, required

DATA 0xffd0149c 0x0000e803	# CPU ODT Control	 (DONE)
DATA 0xffd01480 0x00000001	# DDR Initialization Control	 (DONE)
#bit0=1, enable DDR init upon this register write

# End of Header extension
DATA 0x0 0x0
