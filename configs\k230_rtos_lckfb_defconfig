CONFIG_BOARD_CONFIG_NAME="k230_rtos_lckfb_defconfig"
CONFIG_BOARD_K230_CANMV_LCKFB=y
CONFIG_BOARD_NAME="RtSmart-K230_LCKFB"
CONFIG_BOARD_GEN_IMAGE_CFG_FILE="genimage-sdcard-rtos.cfg"
CONFIG_RTT_AUTO_EXEC_CMD="/sdcard/app/examples/integrated_poc/smart_ipc.elf -K /sdcard/app/examples/integrated_poc/face_detection_320.kmodel -C 1"
CONFIG_RTT_ENABLE_BUILD_EXAMPLES=y
CONFIG_RTT_ENABLE_BUILD_INTEGRATED_EXAMPLES=y
CONFIG_MPP_DEFAULT_SENSOR_CSI_2=y
CONFIG_MPP_ENABLE_CSI_DEV_0=y
CONFIG_MPP_CSI_DEV0_POWER=9
CONFIG_MPP_CSI_DEV0_RESET=29
CONFIG_MPP_ENABLE_CSI_DEV_1=y
CONFIG_MPP_CSI_DEV1_POWER=30
CONFIG_MPP_CSI_DEV1_RESET=31
CONFIG_MPP_ENABLE_CSI_DEV_2=y
CONFIG_MPP_CSI_DEV2_POWER=21
CONFIG_MPP_CSI_DEV2_RESET=13
CONFIG_MPP_CSI_DEV2_I2C_DEV="i2c4"
CONFIG_MPP_ENABLE_SENSOR_GC2093=y
CONFIG_MPP_ENABLE_SENSOR_OV5647=y
CONFIG_MPP_ENABLE_DSI_HDMI=y
CONFIG_MPP_DSI_HDMI_RESET_PIN=24
CONFIG_MPP_ENABLE_DSI_LCD=y
CONFIG_MPP_DSI_LCD_RESET_PIN=22
CONFIG_MPP_DSI_ENABLE_HDMI_LT9611=y
CONFIG_MPP_DSI_ENABLE_LCD_ST7701=y
