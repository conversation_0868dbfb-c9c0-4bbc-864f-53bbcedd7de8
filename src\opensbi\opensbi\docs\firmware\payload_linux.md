Linux as a direct payload to OpenSBI
====================================

OpenSBI has the capability to load a Linux kernel image directly in supervisor
mode. The flattened image generated by the Linux kernel build process can be
provided as a payload to OpenSBI.

Detailed examples can be found in both the [QEMU](../platform/qemu_virt.md)
and the [HiFive Unleashed](../platform/sifive_fu540.md) platform guides.
