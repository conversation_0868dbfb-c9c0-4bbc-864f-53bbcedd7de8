name: CanMV K230 Build for PreRelease

on:
  workflow_dispatch:
    inputs:
      configs:
        description: 'The config list which will compiled'
        required: false
        type: string
        default: "k230_canmv_01studio_defconfig k230_canmv_v1p0p1_defconfig k230_canmv_dongshanpi_defconfig k230_canmv_lckfb_defconfig k230_canmv_v3p0_defconfig k230d_canmv_bpi_zero_defconfig k230d_canmv_atk_dnk230d_defconfig k230_canmv_dongshanpi_defconfig"

jobs:
  build:
    runs-on: ubuntu-24.04
    permissions:
      contents: write
    defaults:
      run:
        shell: bash

    steps:
      - name: Setup SSH
        run: |
          # configure git
          git config --global user.name kendryte747
          git config --global user.email <EMAIL>
          # update ssh key
          mkdir -p ~/.ssh
          echo "${{ secrets.ACTIONS_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

      - name: Install repo
        run: |
          # install repo
          mkdir -p ~/.bin/
          curl https://storage.googleapis.com/git-repo-downloads/repo > ~/.bin/repo
          chmod +x ~/.bin/repo

      - name: Prepare code
        run: |
          rm -rf *
          ls -alh
          ~/.bin/repo init -u **************:canmv-k230/manifest.git
          ~/.bin/repo sync

      - name: Prepare Environment
        run: |
          sudo apt update
          sudo apt install -y bison flex gcc libncurses5-dev pkg-config \
            libconfuse-dev libssl-dev python3 python3-pip python-is-python3 python3-venv \
            cmake libyaml-dev scons mtools bzip2
          python3 -m venv ~/.canmv_venv
          source ~/.canmv_venv/bin/activate
          pip3 install pycryptodome gmssl jsonschema jinja2

      - name: Download toolchains
        run: |
          make dl_toolchain

      - name: Build Projects
        run: |
          source ~/.canmv_venv/bin/activate
          projects=($CONFIGS)
          echo "Config list is ${projects}"
          for proj in ${projects[@]};do
            echo "-------------------"
            echo "build project ${proj}"
            echo "-------------------"
            make ${proj}
            time make log
            echo "-------------------"
          done
        env:
          IS_CI: 1
          CONFIGS: ${{ inputs.configs }}

      - name: Set Env
        run: |
          echo "BUILDTIME=$(TZ=Asia/Shanghai date)" >> $GITHUB_ENV

      - name: Set Relese Txt
        run: |
          cat > prerelease.txt << 'EOF'
          Pre Release自动编译，并会删除旧的编译产物，只保留当前最新镜像，仅供测试使用
          Pre Release build with latest ${{ github.ref_name }} branch, it will delete old build and only keeping the latest version.
          Current ${{ github.ref_name }} branch build at ${{ env.BUILDTIME }} , Current build git commit id ${{ github.sha }}
          EOF

      - name: Upload images to PreRelease Asset
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/heads/main')
        with:
          tag_name: PreRelease
          files: |
            output/**/*.img.gz
            output/**/*.img.gz.md5
          prerelease: true
          generate_release_notes: true
          body_path: prerelease.txt
