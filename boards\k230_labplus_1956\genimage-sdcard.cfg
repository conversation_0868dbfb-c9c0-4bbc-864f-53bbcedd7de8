image bin.vfat {
	vfat {
		label = "BIN"
	}
	size = 10M
	temporary = true
	mountpoint = "bin"
}

image app.vfat {
	vfat {
		label = "SDCARD"
		extraargs = " -s 16 "
	}
	size = 500M
	temporary = true
	mountpoint = "sdcard"
}

image sysimage-sdcard.img {
	hdimage {
		# use mbr
		partition-table-type = "mbr"

		# use gpt
		# partition-table-type = "gpt"
		# disk-uuid = "bc8ba9e1-f3d0-443a-9d55-976a737bde84"
	}

	partition spl {
		in-partition-table = false
		offset = 0x100000
		image = "uboot/fn_u-boot-spl.bin"
	}

	# TODO: Update to use fat partition
	partition uboot_env {
		in-partition-table = false
		offset = 0x1e0000
		size = 0x10000
		image = "uboot/env.bin"
	}

	partition uboot {
		in-partition-table = false
		offset = 0x200000
		image = "uboot/fn_ug_u-boot.bin"
	}

    partition rtt {
		in-partition-table = false
		offset = 10M
		size = 20M
		image = "opensbi/opensbi_rtt_system.bin"
	}

	partition bin {
		# use mbr
		partition-type = 0xc

		# use gpt
		# partition-type-uuid = "F"
		# partition-uuid = "81356e88-d2e9-481a-a1c5-264e22149385"

		offset = 50M
		image = "bin.vfat"
	}

	partition app {
		# use mbr
		partition-type = 0xc

		# use gpt
		# partition-type-uuid = "F"
		# partition-uuid = "64684dc8-f550-4557-ab7e-c6399c348dc1"

		image = "app.vfat"
	}
}
