menu "Board Configuration"
    config BOARD_CHIP_K230
        bool
        default n

    config BOARD_CHIP_K230D
        bool 
        default n

    choice BOARD
        bool "Board Type"
        default BOARD_K230_CANMV

        config BOARD_K230_CANMV
            bool "K230 CanMV, Onboard 512M"
            select BOARD_CHIP_K230

        config BOARD_K230_CANMV_V3P0
            bool "K230 CanMV V3.0, Onboard 1GiB LPDDR4"
            select BOARD_CHIP_K230

        config BOARD_K230_CANMV_LCKFB
            bool "K230 CanMV LCKFB, Onboard 1GiB LPDDR4"
            select BOARD_CHIP_K230

        config BOARD_K230_CANMV_01STUDIO
            bool "K230 CanMV 01 Studio, Onboard 1GiB LPDDR4"
            select BOARD_CHIP_K230

        config BOARD_K230D_CANMV_BPI_ZERO
            bool "K230D CanMV BPI-Zero, SiP 128MiB LPDDR4"
            select BOARD_CHIP_K230D

        config BOARD_K230_CANMV_DONGSHANPI
            bool "K230 CanMV dongshanpi, Onboard 512MiB/1GiB LPDDR3"
            select BOARD_CHIP_K230

        config BOARD_K230_AIHARDWARE
            bool "K230 AiHardWare, Onboard 512M LPDDR4"
            select BOARD_CHIP_K230

        config BOARD_K230_AHW_VEHICLE_V3
            bool "K230 AiHardWare Vehicle V3, Onboard 512M LPDDR4"

        config BOARD_K230D_CANMV_ATK_DNK230D
            bool "K230D CanMV ATK-DNK230D, SiP 128MiB LPDDR4"
            select BOARD_CHIP_K230D

        config BOARD_K230_EVB
            bool "K230 EVB"
            select BOARD_CHIP_K230

        config BOARD_K230D_CANMV_LABPLUS_AI_CAMERA
            bool "K230D CanMV LabPlus Ai Camera"
            select BOARD_CHIP_K230D

        config BOARD_K230_LABPLUS_1956
            bool "K230 CanMV LabPlus 1956"
            select BOARD_CHIP_K230

    endchoice

    config BOARD
        string
        default "k230_canmv" if BOARD_K230_CANMV
        default "k230_canmv_v3p0" if BOARD_K230_CANMV_V3P0
        default "k230_canmv_lckfb" if BOARD_K230_CANMV_LCKFB
        default "k230_canmv_01studio" if BOARD_K230_CANMV_01STUDIO
        default "k230d_canmv_bpi_zero" if BOARD_K230D_CANMV_BPI_ZERO
	    default "k230_canmv_dongshanpi" if BOARD_K230_CANMV_DONGSHANPI
	    default "k230_aihardware" if BOARD_K230_AIHARDWARE
        default "k230_ahw_vehicle_v3" if BOARD_K230_AHW_VEHICLE_V3
        default "k230d_canmv_atk_dnk230d" if BOARD_K230D_CANMV_ATK_DNK230D
        default "k230_evb" if BOARD_K230_EVB
        default "k230d_labplus_ai_camera" if BOARD_K230D_CANMV_LABPLUS_AI_CAMERA
        default "k230_labplus_1956" if BOARD_K230_LABPLUS_1956

    config BOARD_NAME
        string "Board Generate Image Name"
        default "CanMV_K230_V1P0_P1" if BOARD_K230_CANMV
        default "CanMV_K230_V3P0" if BOARD_K230_CANMV_V3P0
        default "CanMV_K230_LCKFB" if BOARD_K230_CANMV_LCKFB
        default "CanMV_K230_01Studio" if BOARD_K230_CANMV_01STUDIO
        default "CanMV_K230D_Zero" if BOARD_K230D_CANMV_BPI_ZERO
        default "K230_aihardware" if BOARD_K230_AIHARDWARE
        default "K230_ahw_vehicle_v3" if BOARD_K230_AHW_VEHICLE_V3
        default "CanMV_K230D_ATK_DNK230D" if BOARD_K230D_CANMV_ATK_DNK230D
        default "K230_EVB" if BOARD_K230_EVB
        default "CanMV_K230D_LabPlusAiCamera" if BOARD_K230D_CANMV_LABPLUS_AI_CAMERA
        default "CanMV_K230_LabPlus_1956" if BOARD_K230_LABPLUS_1956

    config BOARD_GEN_IMAGE_CFG_FILE
        string "Board specified generate image configure file"
        default "genimage-sdcard.cfg"

    menu "Memory Layout"
        config AUTO_DETECT_DDR_SIZE
            bool "Auto Detect DRAM Size"
                default y  if BOARD_K230_CANMV_01STUDIO ||  BOARD_K230_CANMV_DONGSHANPI
                default n

        source "$(SDK_BOARDS_DIR)/Kconfig.memory_auto"
        source "$(SDK_BOARDS_DIR)/Kconfig.memory_static"
    endmenu

    config MEM_BASE_ADDR
        hex
        default MEM_RTSMART_BASE

    config MEM_RTSMART_BASE
        hex
        default 0x0000000
        help
            Should same as uboot dts memory node

    config RTSMART_OPENSIB_MEMORY_SIZE
        hex
        default 0x20000

    source "$(SDK_BOARDS_DIR)/k230_canmv/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_canmv_v3p0/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_canmv_lckfb/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_canmv_01studio/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230d_canmv_bpi_zero/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_canmv_dongshanpi/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_aihardware/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_ahw_vehicle_v3/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230d_canmv_atk_dnk230d/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_evb/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230d_labplus_ai_camera/Kconfig"
    source "$(SDK_BOARDS_DIR)/k230_labplus_1956/Kconfig"

endmenu
