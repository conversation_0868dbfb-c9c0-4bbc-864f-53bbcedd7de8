#include <stdio.h>
#include <pthread.h>
#include <unistd.h>

// 模拟LTE HAL函数声明
extern int es_hal_lte_get_mac(unsigned char *mac);
extern int es_hal_lte_get_rssi(void);
extern int es_hal_lte_init(void);

void* test_get_mac(void* arg) {
    unsigned char mac[6];
    int thread_id = *(int*)arg;
    
    printf("Thread %d: Starting MAC test\n", thread_id);
    
    for (int i = 0; i < 3; i++) {
        printf("Thread %d: Attempt %d to get MAC\n", thread_id, i + 1);
        int ret = es_hal_lte_get_mac(mac);
        if (ret == 0) {
            printf("Thread %d: MAC: %02x:%02x:%02x:%02x:%02x:%02x\n", 
                   thread_id, mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        } else {
            printf("Thread %d: Failed to get MAC, ret=%d\n", thread_id, ret);
        }
        sleep(1);
    }
    
    printf("Thread %d: MAC test completed\n", thread_id);
    return NULL;
}

void* test_get_rssi(void* arg) {
    int thread_id = *(int*)arg;
    
    printf("Thread %d: Starting RSSI test\n", thread_id);
    
    for (int i = 0; i < 3; i++) {
        printf("Thread %d: Attempt %d to get RSSI\n", thread_id, i + 1);
        int rssi = es_hal_lte_get_rssi();
        printf("Thread %d: RSSI: %d\n", thread_id, rssi);
        sleep(1);
    }
    
    printf("Thread %d: RSSI test completed\n", thread_id);
    return NULL;
}

int main() {
    printf("Starting LTE concurrency test...\n");
    
    // 初始化LTE模块
    printf("Initializing LTE module...\n");
    es_hal_lte_init();
    
    pthread_t mac_thread, rssi_thread;
    int mac_thread_id = 1;
    int rssi_thread_id = 2;
    
    // 创建两个线程，一个获取MAC，一个获取RSSI
    printf("Creating threads...\n");
    pthread_create(&mac_thread, NULL, test_get_mac, &mac_thread_id);
    pthread_create(&rssi_thread, NULL, test_get_rssi, &rssi_thread_id);
    
    // 等待线程完成
    pthread_join(mac_thread, NULL);
    pthread_join(rssi_thread, NULL);
    
    printf("LTE concurrency test completed!\n");
    return 0;
}
