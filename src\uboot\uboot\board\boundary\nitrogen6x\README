U-Boot for the Boundary Devices Nitrogen6X and
Freescale i.MX6Q SabreLite boards

This file contains information for the port of
U-Boot to the Boundary Devices Nitrogen6X and
Freescale i.MX6Q SabreLite boards.

1. Boot source, boot from SPI NOR
---------------------------------
The configuration in this directory supports both the
Nitrogen6X and Freescale SabreLite board, but in a
different fashion from Freescale's implementation in
board/freescale/mx6qsabrelite.

In particular, this image supports booting from SPI NOR
and saving the environment to SPI NOR.

It does not support 'boot from SD' at offset 0x400
except through the 'bmode' command.
	http://lists.denx.de/pipermail/u-boot/2012-August/131151.html

2. Boots using 6x_bootscript on SATA or SD card
-----------------------------------------------
The default bootcmd for these boards is configured
to look for and source a boot script named '6x_bootscript'
in the root of the first partition of the following
devices:

	sata 0
	mmc 0
	mmc 1

They're searched in the order listed above, trying both the
ext2 and fat filesystems.

2. Maintaining the SPI NOR
--------------------------
A couple of convenience commands

	clearenv - clear environment to factory default
	upgradeu - look and source a boot script named
		'6x_upgrade' to upgrade the U-Boot version
		in SPI NOR. The search is the same as for
		6x_bootscript described above.

3. Display support
------------------
U-Boot support for the following displays is configured by
default:

    HDMI           - 1024 x 768 for maximum compatibility
    Hannstar-XGA   - 1024 x 768 LVDS (Freescale part number MCIMX-LVDS1)
    wsvga-lvds     - 1024 x 600 LVDS (Boundary p/n Nit6X_1024x600)
    wvga-rgb       - 800 x 480 RGB (Boundary p/n Nit6X_800x480)

Since the ipuv3_fb display driver currently supports only a single display,
this code auto-detects panel by probing the HDMI Phy for Hot Plug Detect
or the I2C touch controller of the LVDS and RGB displays in the priority
listed above.

Setting 'panel' environment variable to one of the names above will
override auto-detection and force activation of the specified panel.

4. Building
------------

To build U-Boot for one of the Nitrogen6x or SabreLite board:

	make nitrogen6x_config
	make

Note that 'nitrogen6x' is a placeholder. The complete list of supported
board configurations is shown in the boards.cfg file:
	nitrogen6q		i.MX6Q/6D	1GB
	nitrogen6dl		i.MX6DL		1GB
	nitrogen6s		i.MX6S		512MB
	nitrogen6q2g		i.MX6Q/6D	2GB
	nitrogen6dl2g		i.MX6DL		2GB
	nitrogen6s1g		i.MX6S		1GB

The -6q variants support either the i.MX6Quad or i.MX6Dual processors
and are configured for a 64-bit memory bus at 1066 MHz.

The -6dl variants also use a 64-bit memory bus, operated at 800MHz.

The -6s variants use a 32-bit memory bus at 800MHz.

If you place the u-boot.imx into a single-partition SD card
along with a binary version of the boot script 6x_upgrade.txt,
you can program it using 'upgradeu':

	U-Boot> run upgradeu
